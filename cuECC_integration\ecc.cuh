#ifndef __LIBSECP256K1_CUH
#define __LIBSECP256K1_CUH

#include "curve/point.cuh"
#include "curve/secp256k1.cuh"

// 🔧 CUDA兼容性宏定义
#ifndef __CUDACC__
#define __global__
#define __device__
#define __host__
#endif

// ✅ 原有的cuECC接口（修复后）
__global__ void getPublicKeyByPrivateKeyKernel(cuECC_Point_Internal *output,
                                               const u64 privateKey[4]);

extern "C" void getPublicKeyByPrivateKey(cuECC_Point_Internal output[], u64 privateKeys[][4],
                                         int n);

// 🔧 适配器接口：兼容我们现有的代码
// 将cuECC的Point类型映射到我们的接口
typedef cuECC_Point_Internal cuECC_Point;
typedef u64 cuECC_u64;

// 提供与我们现有代码兼容的函数接口声明
#ifdef __CUDACC__
__device__ inline void cuECC_secp256k1_ecmult_gen(cuECC_Point *result, const u64 *scalar);

// 用于测试的简单内核声明
__global__ void test_cuECC_ecmult_gen(cuECC_Point *results, const u64 *scalars, int count);
#endif

#endif
