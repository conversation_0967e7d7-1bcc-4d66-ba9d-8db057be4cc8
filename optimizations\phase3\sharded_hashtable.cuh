/**
 * @file sharded_hashtable.cuh
 * @brief 分片哈希表系统 - Phase 3内存优化
 * 
 * 实现多级分片哈希表，支持大范围DP存储和快速碰撞检测
 * 针对512-bit数据和125-bit+范围优化
 */

#ifndef SHARDED_HASHTABLE_CUH
#define SHARDED_HASHTABLE_CUH

#include <cuda_runtime.h>
#include <cstdint>

// 512-bit数据结构（与kang_per_sm_kernel.cu保持一致）
struct uint512_t {
    uint64_t d[8];  // 8个64位整数 = 512位
    
    __device__ __host__ __forceinline__ uint512_t() {
        #pragma unroll
        for(int i = 0; i < 8; i++) d[i] = 0;
    }
    
    __device__ __host__ __forceinline__ bool operator==(const uint512_t& other) const {
        #pragma unroll
        for(int i = 0; i < 8; i++) {
            if(d[i] != other.d[i]) return false;
        }
        return true;
    }
    
    __device__ __host__ __forceinline__ bool is_zero() const {
        #pragma unroll
        for(int i = 0; i < 8; i++) {
            if(d[i] != 0) return false;
        }
        return true;
    }
};

/**
 * @brief Distinguished Point条目 - 512-bit优化版本
 */
struct DP_Entry_512 {
    uint512_t x;        // X坐标 (512-bit)
    uint512_t y;        // Y坐标 (512-bit)
    uint512_t distance; // 距离 (512-bit)
    uint32_t type;      // 袋鼠类型 (0=驯服, 1=野生)
    uint32_t thread_id; // 线程ID
    uint64_t timestamp; // 时间戳
    
    __device__ __host__ __forceinline__ DP_Entry_512() {
        type = 0;
        thread_id = 0;
        timestamp = 0;
    }
    
    __device__ __host__ __forceinline__ bool is_valid() const {
        return !x.is_zero();
    }
};

/**
 * @brief 分片哈希表配置
 */
struct ShardedHashConfig {
    static const int MAX_SHARDS = 64;           // 最大分片数
    static const int ENTRIES_PER_SHARD = 1024*1024; // 每分片条目数
    static const int HASH_BUCKET_SIZE = 16;     // 哈希桶大小
    static const int MAX_PROBE_DISTANCE = 32;   // 最大探测距离
};

/**
 * @brief 单个哈希分片
 */
struct HashShard_512 {
    DP_Entry_512 *entries;      // 条目数组
    uint32_t *occupancy;        // 占用标记
    uint32_t capacity;          // 容量
    uint32_t count;             // 当前条目数
    uint32_t shard_id;          // 分片ID
    
    __device__ __host__ __forceinline__ HashShard_512() {
        entries = nullptr;
        occupancy = nullptr;
        capacity = 0;
        count = 0;
        shard_id = 0;
    }
};

/**
 * @brief 分片哈希表主类
 */
class ShardedHashTable_512 {
private:
    HashShard_512 *shards;          // 分片数组
    int num_shards;                 // 分片数量
    size_t total_capacity;          // 总容量
    size_t total_count;             // 总条目数
    
    // GPU内存指针
    HashShard_512 *dev_shards;
    DP_Entry_512 *dev_entries_pool;
    uint32_t *dev_occupancy_pool;
    
public:
    /**
     * @brief 构造函数
     */
    ShardedHashTable_512(int shards = 32);
    
    /**
     * @brief 析构函数
     */
    ~ShardedHashTable_512();
    
    /**
     * @brief 初始化GPU内存
     */
    bool initialize_gpu_memory();
    
    /**
     * @brief 释放GPU内存
     */
    void cleanup_gpu_memory();
    
    /**
     * @brief 计算分片索引
     */
    __device__ __host__ __forceinline__ 
    int get_shard_index(const uint512_t &key) const {
        // 使用X坐标的高位进行分片
        uint64_t hash = key.d[0] ^ key.d[1] ^ key.d[2] ^ key.d[3];
        return hash % num_shards;
    }
    
    /**
     * @brief 计算哈希值
     */
    __device__ __host__ __forceinline__ 
    uint32_t compute_hash(const uint512_t &key) const {
        // 512-bit哈希函数
        uint64_t h1 = key.d[0] ^ key.d[4];
        uint64_t h2 = key.d[1] ^ key.d[5];
        uint64_t h3 = key.d[2] ^ key.d[6];
        uint64_t h4 = key.d[3] ^ key.d[7];
        
        uint64_t hash = h1 ^ (h2 << 16) ^ (h3 << 32) ^ (h4 << 48);
        return (uint32_t)(hash ^ (hash >> 32));
    }
    
    /**
     * @brief 获取统计信息
     */
    void get_statistics(size_t &total_entries, double &load_factor, int &max_shard_load) const;
    
    /**
     * @brief 重置哈希表
     */
    void reset();
};

/**
 * @brief GPU内核：插入DP条目
 */
__global__ void insert_dp_entries_kernel(
    HashShard_512 *shards,
    const DP_Entry_512 *entries,
    int num_entries,
    int num_shards
);

/**
 * @brief GPU内核：查找DP碰撞
 */
__global__ void find_dp_collisions_kernel(
    HashShard_512 *shards,
    const DP_Entry_512 *query_entries,
    DP_Entry_512 *collision_results,
    int *collision_found,
    int num_queries,
    int num_shards
);

/**
 * @brief GPU内核：批量DP处理
 */
__global__ void batch_dp_processing_kernel(
    HashShard_512 *shards,
    DP_Entry_512 *dp_buffer,
    int *collision_results,
    int buffer_size,
    int num_shards,
    uint32_t dp_mask
);

#endif // SHARDED_HASHTABLE_CUH
