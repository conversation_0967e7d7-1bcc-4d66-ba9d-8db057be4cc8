#ifndef PLATFORM_UTILS_H
#define PLATFORM_UTILS_H

#include <stdint.h>
#include <string>

/**
 * @file platform_utils.h
 * @brief 跨平台兼容性工具
 * 
 * 提供Windows和Linux平台的统一接口
 */

#ifdef _WIN32
    #define PLATFORM_WINDOWS
    #define NOMINMAX  // 防止Windows.h定义min/max宏
    #include <windows.h>
#else
    #define PLATFORM_LINUX
    #include <unistd.h>
    #include <sys/time.h>
    #include <pthread.h>
#endif

class PlatformUtils {
public:
    /**
     * @brief 初始化平台相关功能
     */
    static void initializePlatform();
    
    /**
     * @brief 清理平台相关资源
     */
    static void cleanupPlatform();
    
    /**
     * @brief 获取当前时间戳 (毫秒)
     * @return 时间戳
     */
    static uint64_t getCurrentTimeMs();
    
    /**
     * @brief 获取高精度时间戳 (微秒)
     * @return 时间戳
     */
    static uint64_t getCurrentTimeUs();
    
    /**
     * @brief 休眠指定毫秒数
     * @param milliseconds 毫秒数
     */
    static void sleepMs(uint32_t milliseconds);
    
    /**
     * @brief 休眠指定微秒数
     * @param microseconds 微秒数
     */
    static void sleepUs(uint32_t microseconds);
    
    /**
     * @brief 获取CPU核心数
     * @return CPU核心数
     */
    static int getCPUCoreCount();
    
    /**
     * @brief 获取系统总内存 (字节)
     * @return 系统内存大小
     */
    static size_t getSystemMemory();
    
    /**
     * @brief 获取可用内存 (字节)
     * @return 可用内存大小
     */
    static size_t getAvailableMemory();
    
    /**
     * @brief 获取平台名称
     * @return 平台名称字符串
     */
    static std::string getPlatformName();
    
    /**
     * @brief 获取系统架构
     * @return 架构名称 (x64, ARM64等)
     */
    static std::string getSystemArchitecture();
    
    /**
     * @brief 设置线程优先级
     * @param priority 优先级 (-20到20, 0为默认)
     */
    static void setThreadPriority(int priority);
    
    /**
     * @brief 设置进程优先级
     * @param priority 优先级 (-20到20, 0为默认)
     */
    static void setProcessPriority(int priority);
    
    /**
     * @brief 获取页面大小
     * @return 页面大小 (字节)
     */
    static size_t getPageSize();
    
    /**
     * @brief 锁定内存页面 (防止交换)
     * @param addr 内存地址
     * @param size 内存大小
     * @return 是否成功
     */
    static bool lockMemory(void* addr, size_t size);
    
    /**
     * @brief 解锁内存页面
     * @param addr 内存地址
     * @param size 内存大小
     * @return 是否成功
     */
    static bool unlockMemory(void* addr, size_t size);
    
    /**
     * @brief 分配大页内存
     * @param size 内存大小
     * @return 内存指针，失败返回nullptr
     */
    static void* allocateHugePages(size_t size);
    
    /**
     * @brief 释放大页内存
     * @param ptr 内存指针
     * @param size 内存大小
     */
    static void freeHugePages(void* ptr, size_t size);
    
    /**
     * @brief 检查是否支持大页内存
     * @return 是否支持
     */
    static bool supportsHugePages();
    
    /**
     * @brief 获取网络字节序的32位整数
     * @param host_int 主机字节序整数
     * @return 网络字节序整数
     */
    static uint32_t hostToNetwork32(uint32_t host_int);
    
    /**
     * @brief 获取主机字节序的32位整数
     * @param net_int 网络字节序整数
     * @return 主机字节序整数
     */
    static uint32_t networkToHost32(uint32_t net_int);
    
    /**
     * @brief 创建目录
     * @param path 目录路径
     * @return 是否成功
     */
    static bool createDirectory(const std::string& path);
    
    /**
     * @brief 检查文件是否存在
     * @param path 文件路径
     * @return 是否存在
     */
    static bool fileExists(const std::string& path);
    
    /**
     * @brief 获取文件大小
     * @param path 文件路径
     * @return 文件大小，失败返回-1
     */
    static int64_t getFileSize(const std::string& path);
    
    /**
     * @brief 获取环境变量
     * @param name 变量名
     * @param default_value 默认值
     * @return 变量值
     */
    static std::string getEnvironmentVariable(const std::string& name, 
                                            const std::string& default_value = "");
    
    /**
     * @brief 设置环境变量
     * @param name 变量名
     * @param value 变量值
     * @return 是否成功
     */
    static bool setEnvironmentVariable(const std::string& name, const std::string& value);
    
private:
    static bool platform_initialized;
    
#ifdef PLATFORM_WINDOWS
    static LARGE_INTEGER frequency;
    static bool frequency_initialized;
#endif
};

/**
 * @brief 平台相关的线程类
 */
class PlatformThread {
public:
    PlatformThread();
    ~PlatformThread();
    
    /**
     * @brief 启动线程
     * @param func 线程函数
     * @param arg 线程参数
     * @return 是否成功
     */
    bool start(void* (*func)(void*), void* arg);
    
    /**
     * @brief 等待线程结束
     * @return 线程返回值
     */
    void* join();
    
    /**
     * @brief 分离线程
     */
    void detach();
    
    /**
     * @brief 获取线程ID
     * @return 线程ID
     */
    uint64_t getId() const;
    
private:
#ifdef PLATFORM_WINDOWS
    HANDLE thread_handle;
    DWORD thread_id;
#else
    pthread_t thread;
#endif
    bool started;
    bool joined;
};

/**
 * @brief 平台相关的互斥锁类
 */
class PlatformMutex {
public:
    PlatformMutex();
    ~PlatformMutex();
    
    /**
     * @brief 加锁
     */
    void lock();
    
    /**
     * @brief 尝试加锁
     * @return 是否成功
     */
    bool tryLock();
    
    /**
     * @brief 解锁
     */
    void unlock();
    
private:
#ifdef PLATFORM_WINDOWS
    CRITICAL_SECTION cs;
#else
    pthread_mutex_t mutex;
#endif
};

#endif // PLATFORM_UTILS_H
