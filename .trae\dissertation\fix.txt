# Bernstein-<PERSON>表生成器代码分析与修正

我将根据论文《Computing small discrete logarithms faster》的内容对您的代码进行详细分析和修正。以下是主要问题和改进建议：

## 1. 参数计算问题

### 问题分析
在`CalculateParameters`函数中，W值的计算不符合论文公式。论文公式应为：
```
W = (ℓ^(1/3) * T^(2/3)) / α^(2/3)
```
其中ℓ是区间长度，T是表大小，α是参数。

### 修正代码
```cpp
void BernsteinLangeTableGenerator::CalculateParameters(int range_bits) {
    printf("🔧 按Bernstein-Lange论文公式计算参数...\n");
    
    // 计算区间长度 ℓ = 2^range_bits
    double interval_length = pow(2.0, range_bits);
    
    // 🎯 步骤1：严格按照Bernstein-Lange论文公式计算理论W值
    // 论文公式：W = (ℓ^(1/3) × T^(2/3)) / α^(2/3)
    double log2_W_theory = (range_bits / 3.0) + (2.0 * hash_bits / 3.0) - (2.0 * log2(alpha_param) / 3.0);
    
    // 🚨 关键修复：对于大范围，使用对数计算避免溢出
    printf("📊 理论log2(W) = %.2f (按论文公式)\n", log2_W_theory);
    
    // 处理大数值情况
    if (log2_W_theory > 63) {
        printf("⚠️ 警告: 理论W值过大 (2^%.1f)，超出uint64_t范围\n", log2_W_theory);
        walk_length_W = UINT64_MAX;
        printf("✅ 使用截断W值: %lu (保持算法正确性)\n", (unsigned long)walk_length_W);
    } else {
        walk_length_W = (uint64_t)pow(2.0, log2_W_theory);
        if (walk_length_W == 0) walk_length_W = 1;
        printf("✅ 理论W值: %lu (严格按论文计算)\n", (unsigned long)walk_length_W);
    }
    
    // 🎯 步骤2：严格按照论文设置区分点概率
    // 论文建议：区分点概率应该使得期望区分点距离约为 W/c，其中c是常数(通常2-4)
    // 即：1/p ≈ W/c，所以 p ≈ c/W
    uint64_t target_dp_distance;
    if (walk_length_W == UINT64_MAX) {
        // 对于极大W值，使用基于表大小的概率
        target_dp_distance = table_size_T * 4;
    } else {
        target_dp_distance = walk_length_W / 2;  // 使用c=2
    }
    
    // 计算dp_mask_bits使得2^dp_mask_bits ≈ target_dp_distance
    dp_mask_bits = (int)ceil(log2(target_dp_distance));
    
    // 确保在合理范围内
    if (dp_mask_bits < 12) dp_mask_bits = 12;
    if (dp_mask_bits > 32) dp_mask_bits = 32;
    
    printf("🎯 按论文理论设置区分点概率: dp_mask_bits = %d (基于W值)\n", dp_mask_bits);
    printf("🎯 区分点概率设置：dp_mask_bits = %d, 概率 = 1/2^%d = 1/%d\n",
           dp_mask_bits, dp_mask_bits, 1 << dp_mask_bits);
    
    // 🎯 步骤3：严格按照论文设置实际游走长度
    // 论文建议：实际游走长度应该是理论W值的倍数，通常是2-4倍
    uint64_t theoretical_max_walk;
    if (walk_length_W == UINT64_MAX) {
        // 对于极大W值，使用基于表大小的游走长度
        theoretical_max_walk = table_size_T * 16;
    } else {
        theoretical_max_walk = walk_length_W * 4;  // 4倍理论W值
    }
    
    // 确保实际游走长度合理
    uint64_t dp_expected_distance = 1ULL << dp_mask_bits;
    actual_walk_length_for_gpu = std::min(theoretical_max_walk, dp_expected_distance * 2);
    
    printf("🎯 游走长度设置: 理论4W=%lu, 期望2×dp_distance=%lu, 选择=%lu\n",
           (unsigned long)theoretical_max_walk,
           (unsigned long)(dp_expected_distance * 2),
           (unsigned long)actual_walk_length_for_gpu);
    
    // 验证论文理论成功概率
    double success_prob = 1.0 - exp(-alpha_param * alpha_param);
    printf("  理论成功概率: 1 - exp(-α²) = %.3f%% (论文预期)\n", success_prob * 100.0);
    
    printf("✅ 参数计算完成，符合Bernstein-Lange论文要求\n");
}
```

## 2. 步长生成问题

### 问题分析
在`GenerateDynamicSteps`函数中，步长指数的范围设置不正确。根据论文，步长指数应在[0, L/(4W)]范围内随机选取，其中L是区间长度。

### 修正代码
```cpp
bool BernsteinLangeTableGenerator::GenerateDynamicSteps() {
    printf("\n🔧 生成论文要求的动态步长...\n");
    
    // 🚨 关键修复：初始化随机数种子
    unsigned long seed = (unsigned long)time(nullptr);
    rseed(seed);
    printf("  随机种子初始化: %lu\n", seed);
    
    // 创建区间长度对象
    Int interval_length_int;
    interval_length_int.bits64[0] = interval_length[0];
    interval_length_int.bits64[1] = interval_length[1];
    interval_length_int.bits64[2] = interval_length[2];
    interval_length_int.bits64[3] = interval_length[3];
    
    // 🎯 按照论文要求计算步长范围上限：L/(4W)
    Int step_range_limit;
    
    // 处理大W值情况
    if (walk_length_W == UINT64_MAX || walk_length_W == 0) {
        // 对于极大W值，使用L/2^20作为范围上限
        step_range_limit.Set(&interval_length_int);
        step_range_limit.ShiftR(20);  // L/2^20
        
        if (step_range_limit.IsZero()) {
            step_range_limit.SetQWord(0, 1);
            printf("⚠️ 警告: 步长范围过小，设置为最小值1\n");
        }
        
        printf("  使用替代步长范围上限 L/2^20: 0x%s\n", step_range_limit.GetBase16().c_str());
    } else {
        // 正常情况：计算 L/(4W)
        Int fourW;
        fourW.SetQWord(0, 4);
        fourW.Mult(walk_length_W);
        
        // 执行除法 L/(4W)
        step_range_limit.Set(&interval_length_int);
        step_range_limit.Div(&fourW);
        
        // 确保步长范围至少为1
        if (step_range_limit.IsZero()) {
            step_range_limit.SetQWord(0, 1);
            printf("⚠️ 警告: 步长范围过小，设置为最小值1\n");
        }
        
        printf("  步长范围上限 L/(4W): 0x%s\n", step_range_limit.GetBase16().c_str());
    }
    
    // 🎯 使用Int库生成精确的随机步长指数
    for (int i = 0; i < NUM_STEPS; i++) {
        // 生成[0, step_range_limit]范围内的随机数
        Int step_scalar;
        step_scalar.Rand(&step_range_limit);
        
        printf("  步长%d指数: 0x%s\n", i, step_scalar.GetBase16().c_str());
        
        // 存储到数组格式（用于GPU传递）
        step_exponents[i][0] = step_scalar.bits64[0];
        step_exponents[i][1] = step_scalar.bits64[1];
        step_exponents[i][2] = step_scalar.bits64[2];
        step_exponents[i][3] = step_scalar.bits64[3];
        
        // 计算椭圆曲线点
        Point cpu_step_point;
        try {
            if (!secp) {
                printf("❌ 错误: secp指针为空\n");
                return false;
            }
            
            if (step_scalar.IsZero()) {
                printf("⚠️ 警告: 步长%d为零，使用默认值1\n", i);
                step_scalar.SetQWord(0, 1);
            }
            
            cpu_step_point = secp->ComputePublicKey(&step_scalar);
        } catch (const std::exception& e) {
            printf("❌ 错误: 椭圆曲线点计算失败 (步长%d): %s\n", i, e.what());
            return false;
        } catch (...) {
            printf("❌ 错误: 椭圆曲线点计算发生未知异常 (步长%d)\n", i);
            return false;
        }
        
        // 转换为cuECC格式
        step_points_x[i][0] = cpu_step_point.x.bits64[0];
        step_points_x[i][1] = cpu_step_point.x.bits64[1];
        step_points_x[i][2] = cpu_step_point.x.bits64[2];
        step_points_x[i][3] = cpu_step_point.x.bits64[3];
        step_points_y[i][0] = cpu_step_point.y.bits64[0];
        step_points_y[i][1] = cpu_step_point.y.bits64[1];
        step_points_y[i][2] = cpu_step_point.y.bits64[2];
        step_points_y[i][3] = cpu_step_point.y.bits64[3];
        
        // 调试：打印步长点坐标
        if (i < 3) {  // 只打印前3个步长点避免过多输出
            printf("  步长%d点坐标:\n", i);
            printf("    x: 0x%016lx%016lx%016lx%016lx\n",
                   PRIx64_CAST(step_points_x[i][3]), PRIx64_CAST(step_points_x[i][2]),
                   PRIx64_CAST(step_points_x[i][1]), PRIx64_CAST(step_points_x[i][0]));
            printf("    y: 0x%016lx%016lx%016lx%016lx\n",
                   PRIx64_CAST(step_points_y[i][3]), PRIx64_CAST(step_points_y[i][2]),
                   PRIx64_CAST(step_points_y[i][1]), PRIx64_CAST(step_points_y[i][0]));
        }
    }
    
    printf("✅ 动态步长生成完成\n");
    return true;
}
```

## 3. 随机起点生成问题

### 问题分析
在`LaunchCandidateGenerationKernel`函数中，随机起点生成不符合论文要求。论文要求在区间[A, A+L-1]内随机生成起点。

### 修正代码
```cpp
bool BernsteinLangeTableGenerator::LaunchCandidateGenerationKernel() {
    // ... 前面的代码保持不变 ...
    
    // 🎯 关键修复：按论文要求在[A, A+L-1]范围内生成随机起点
    printf("使用Int库预生成随机起点 (论文要求: [A, A+L-1])...\n");
    
    // 创建区间起始值和长度的Int对象
    Int interval_start_int, interval_length_int;
    interval_start_int.SetInt32(0);
    interval_length_int.SetInt32(0);
    
    // 设置bits64数组
    interval_start_int.bits64[0] = interval_start[0];
    interval_start_int.bits64[1] = interval_start[1];
    interval_start_int.bits64[2] = interval_start[2];
    interval_start_int.bits64[3] = interval_start[3];
    interval_length_int.bits64[0] = interval_length[0];
    interval_length_int.bits64[1] = interval_length[1];
    interval_length_int.bits64[2] = interval_length[2];
    interval_length_int.bits64[3] = interval_length[3];
    
    // 计算区间结束值用于显示
    Int interval_end;
    interval_end.Set(&interval_start_int);
    interval_end.Add(&interval_length_int);
    printf("  区间: [0x%s, 0x%s]\n",
           interval_start_int.GetBase16().c_str(),
           interval_end.GetBase16().c_str());
    
    // 🎯 严格按照论文计算所需的游走数
    uint64_t expected_dp_distance = 1ULL << dp_mask_bits;
    double success_rate = 1.0 - exp(-(double)actual_walk_length_for_gpu / expected_dp_distance);
    uint64_t total_walks_needed = (uint64_t)(candidate_size_N / success_rate * 1.2);
    
    printf("🎯 按论文计算游走需求:\n");
    printf("  期望区分点距离: %lu 步\n", (unsigned long)expected_dp_distance);
    printf("  游走成功率: %.3f\n", success_rate);
    printf("  目标候选数: %lu\n", (unsigned long)candidate_size_N);
    printf("  需要预生成 %lu 个随机起点\n", PRIu64_CAST(total_walks_needed));
    
    // 分配CPU端随机起点数组
    uint64_t (*random_start_points)[4] = new uint64_t[total_walks_needed][4];
    
    // 🚨 关键修复：按论文要求生成[A, A+L-1]范围内的随机起点
    for (uint64_t i = 0; i < total_walks_needed; i++) {
        // 步骤1：生成[0, L-1]范围内的偏移量
        Int offset_y;
        offset_y.Rand(&interval_length_int);
        
        // 步骤2：计算实际起点 y = A + offset_y
        Int actual_y;
        actual_y.Set(&interval_start_int);
        actual_y.Add(&offset_y);  // y = A + offset_y
        
        // 存储到数组
        random_start_points[i][0] = actual_y.bits64[0];
        random_start_points[i][1] = actual_y.bits64[1];
        random_start_points[i][2] = actual_y.bits64[2];
        random_start_points[i][3] = actual_y.bits64[3];
        
        // 进度显示
        if (i % 10000 == 0 && i > 0) {
            printf("  已生成 %lu/%lu 个随机起点\r", PRIu64_CAST(i), PRIu64_CAST(total_walks_needed));
            fflush(stdout);
        }
    }
    
    printf("\n✅ 随机起点预生成完成\n");
    
    // ... 后面的代码保持不变 ...
}
```

## 4. 权重计算问题

### 问题分析
在`PerformCPUUtilitySelection`函数中，权重计算不符合论文公式。论文要求权重 = 总步数 + 4W * 碰撞次数。

### 修正代码
```cpp
bool BernsteinLangeTableGenerator::PerformCPUUtilitySelection() {
    // ... 前面的代码保持不变 ...
    
    // 严格按照论文公式累计权重
    for (uint64_t i = 0; i < total_dp_found; i++) {
        const BLCandidateEntry &candidate = host_candidate_table[i];
        uint32_t hash = candidate.dp_hash;
        uint32_t steps = candidate.walk_steps;
        
        // 🔧 添加边界检查：确保哈希值在有效范围内
        if (hash >= (1ULL << hash_bits)) {
            printf("⚠️ 警告: 无效哈希值 0x%08X (超出范围)\n", hash);
            continue;
        }
        
        // 累计总步数（论文要求的是总和，不是平均）
        dp_weights[hash].total_walk_length += steps;
        dp_weights[hash].count++;
        
        // 更新最短和最长路径
        if (steps < dp_weights[hash].min_steps) {
            dp_weights[hash].min_steps = steps;
            dp_weights[hash].representative = candidate;  // 选择最短路径作为代表
        }
        if (steps > dp_weights[hash].max_steps) {
            dp_weights[hash].max_steps = steps;
        }
    }
    
    printf("找到 %zu 个唯一区分点\n", dp_weights.size());
    
    // 创建权重排序列表
    struct WeightedDP {
        uint32_t hash;
        uint64_t weight;
        uint64_t collision_count;
        uint64_t avg_steps;
        BLCandidateEntry entry;
    };
    
    std::vector<WeightedDP> weighted_dps;
    weighted_dps.reserve(dp_weights.size());
    
    for (const auto &pair : dp_weights) {
        uint32_t hash = pair.first;
        const WeightInfo &info = pair.second;
        
        // 🎯 严格按照论文权重公式: total_walk_length + 4W * count
        uint64_t weight = info.total_walk_length + (4ULL * walk_length_W * info.count);
        uint64_t avg_steps = info.total_walk_length / info.count;
        
        min_weight = (min_weight < weight) ? min_weight : weight;
        max_weight = (max_weight > weight) ? max_weight : weight;
        
        weighted_dps.push_back({hash, weight, info.count, avg_steps, info.representative});
    }
    
    printf("  权重范围: %lu - %lu (比率 %.2fx)\n",
           PRIu64_CAST(min_weight), PRIu64_CAST(max_weight), (double)max_weight / min_weight);
    
    // 按权重降序排序
    std::sort(weighted_dps.begin(), weighted_dps.end(),
              [](const WeightedDP &a, const WeightedDP &b) {
                  return a.weight > b.weight;
              });
    
    // ... 后面的代码保持不变 ...
}
```

## 5. 表填充策略问题

### 问题分析
在表填充部分，当前策略可能不符合论文要求。论文建议选择权重最高的T个区分点。

### 修正代码
```cpp
bool BernsteinLangeTableGenerator::PerformCPUUtilitySelection() {
    // ... 前面的代码保持不变直到权重排序 ...
    
    printf("权重排序完成，开始填充最终表...\n");
    
    // 分配最终表CPU内存
    host_final_table = (BLPrecomputeEntry*)malloc(table_size_T * sizeof(BLPrecomputeEntry));
    if (!host_final_table) {
        printf("❌ 最终表CPU内存分配失败\n");
        return false;
    }
    
    // 清零最终表
    memset(host_final_table, 0, table_size_T * sizeof(BLPrecomputeEntry));
    
    uint64_t filled_entries = 0;
    
    // 🎯 策略1: 填充权重最高的T个区分点
    for (uint64_t i = 0; i < weighted_dps.size() && filled_entries < table_size_T; i++) {
        const WeightedDP &selected = weighted_dps[i];
        const BLCandidateEntry &candidate = selected.entry;
        BLPrecomputeEntry &final_entry = host_final_table[filled_entries];
        
        // 复制椭圆曲线点
        for (int j = 0; j < 4; j++) {
            final_entry.dp_x[j] = candidate.dp_x[j];
            // 计算完整离散对数: y + d
            final_entry.discrete_log[j] = candidate.start_log[j] + candidate.walk_distance[j];
        }
        final_entry.dp_y_sign = candidate.dp_y_sign;
        final_entry.dp_hash = candidate.dp_hash;
        final_entry.utility_weight = (uint32_t)selected.weight;
        
        filled_entries++;
    }
    
    // 🎯 策略2: 如果还有空间，使用重复的高权重区分点填充
    if (filled_entries < table_size_T) {
        printf("策略2: 重复使用高权重区分点填充剩余空间...\n");
        uint64_t remaining_space = table_size_T - filled_entries;
        
        // 循环使用权重最高的区分点
        for (uint64_t i = 0; i < remaining_space; i++) {
            const WeightedDP &selected = weighted_dps[i % weighted_dps.size()];
            const BLCandidateEntry &candidate = selected.entry;
            BLPrecomputeEntry &final_entry = host_final_table[filled_entries];
            
            // 复制椭圆曲线点
            for (int j = 0; j < 4; j++) {
                final_entry.dp_x[j] = candidate.dp_x[j];
                final_entry.discrete_log[j] = candidate.start_log[j] + candidate.walk_distance[j];
            }
            final_entry.dp_y_sign = candidate.dp_y_sign;
            final_entry.dp_hash = candidate.dp_hash;
            final_entry.utility_weight = (uint32_t)selected.weight;
            
            filled_entries++;
        }
        
        printf("策略2完成，额外填充了 %lu 个条目\n", PRIu64_CAST(filled_entries - weighted_dps.size()));
    }
    
    // 打印详细统计信息
    printf("✅ CPU权重累计和选择完成\n");
    printf("  从 %lu 个候选中找到 %zu 个唯一区分点\n", PRIu64_CAST(total_dp_found), weighted_dps.size());
    printf("  最终填充了 %lu 个表项\n", PRIu64_CAST(filled_entries));
    printf("  最终表填充率: %.2f%%\n", (double)filled_entries / table_size_T * 100.0);
    
    // 打印权重分布的详细信息
    if (weighted_dps.size() > 0) {
        printf("权重分布详情:\n");
        printf("  最高权重DP: 哈希=0x%08X, 权重=%lu, 碰撞=%lu次, 平均步数=%lu\n",
               weighted_dps[0].hash, PRIu64_CAST(weighted_dps[0].weight),
               PRIu64_CAST(weighted_dps[0].collision_count), PRIu64_CAST(weighted_dps[0].avg_steps));
        
        if (weighted_dps.size() > 1) {
            size_t mid = weighted_dps.size() / 2;
            printf("  中位权重DP: 哈希=0x%08X, 权重=%lu, 碰撞=%lu次, 平均步数=%lu\n",
                   weighted_dps[mid].hash, PRIu64_CAST(weighted_dps[mid].weight),
                   PRIu64_CAST(weighted_dps[mid].collision_count), PRIu64_CAST(weighted_dps[mid].avg_steps));
            
            size_t last = weighted_dps.size() - 1;
            printf("  最低权重DP: 哈希=0x%08X, 权重=%lu, 碰撞=%lu次, 平均步数=%lu\n",
                   weighted_dps[last].hash, PRIu64_CAST(weighted_dps[last].weight),
                   PRIu64_CAST(weighted_dps[last].collision_count), PRIu64_CAST(weighted_dps[last].avg_steps));
        }
    }
    
    return true;
}
```

## 6. 内存管理问题

### 问题分析
在内存分配和释放部分，存在一些潜在问题，如内存泄漏和边界检查不足。

### 修正代码
```cpp
bool BernsteinLangeTableGenerator::AllocateGPUMemory() {
    printf("💾 分配GPU内存...\n");
    
    cudaError_t err;
    
    // 候选表内存
    size_t candidate_size = candidate_size_N * sizeof(BLCandidateEntry);
    err = cudaMalloc(&dev_candidate_table, candidate_size);
    if (err != cudaSuccess) {
        printf("❌ 候选表内存分配失败: %s\n", cudaGetErrorString(err));
        return false;
    }
    
    // 最终表内存
    size_t final_size = table_size_T * sizeof(BLPrecomputeEntry);
    err = cudaMalloc(&dev_final_table, final_size);
    if (err != cudaSuccess) {
        printf("❌ 最终表内存分配失败: %s\n", cudaGetErrorString(err));
        cudaFree(dev_candidate_table);  // 释放已分配的内存
        return false;
    }
    
    // 🔧 关键修复：动态计算实际需要的线程数
    int blocks_needed = (int)((candidate_size_N + BL_THREADS_PER_BLOCK - 1) / BL_THREADS_PER_BLOCK);
    int max_blocks = (65535 < blocks_needed) ? 65535 : blocks_needed;
    int actual_threads = max_blocks * BL_THREADS_PER_BLOCK;
    printf("  实际分配线程数: %d (blocks: %d × threads: %d)\n",
           actual_threads, max_blocks, BL_THREADS_PER_BLOCK);
    
    // 🔧 存储实际线程数，供后续边界检查使用
    this->actual_max_threads = actual_threads;
    
    // 计数器内存
    err = cudaMalloc(&dev_dp_counter, sizeof(uint32_t));
    if (err != cudaSuccess) {
        printf("❌ 计数器内存分配失败: %s\n", cudaGetErrorString(err));
        cudaFree(dev_candidate_table);
        cudaFree(dev_final_table);
        return false;
    }
    
    // 🎯 分配区间参数GPU内存
    err = cudaMalloc(&dev_interval_start, 4 * sizeof(uint64_t));
    if (err != cudaSuccess) {
        printf("❌ 区间起始值内存分配失败: %s\n", cudaGetErrorString(err));
        cudaFree(dev_candidate_table);
        cudaFree(dev_final_table);
        cudaFree(dev_dp_counter);
        return false;
    }
    
    err = cudaMalloc(&dev_interval_length, 4 * sizeof(uint64_t));
    if (err != cudaSuccess) {
        printf("❌ 区间长度内存分配失败: %s\n", cudaGetErrorString(err));
        cudaFree(dev_candidate_table);
        cudaFree(dev_final_table);
        cudaFree(dev_dp_counter);
        cudaFree(dev_interval_start);
        return false;
    }
    
    // 🎯 复制区间参数到GPU
    err = cudaMemcpy(dev_interval_start, interval_start, 4 * sizeof(uint64_t), cudaMemcpyHostToDevice);
    if (err != cudaSuccess) {
        printf("❌ 区间起始值复制失败: %s\n", cudaGetErrorString(err));
        // 释放所有已分配的内存
        cudaFree(dev_candidate_table);
        cudaFree(dev_final_table);
        cudaFree(dev_dp_counter);
        cudaFree(dev_interval_start);
        cudaFree(dev_interval_length);
        return false;
    }
    
    err = cudaMemcpy(dev_interval_length, interval_length, 4 * sizeof(uint64_t), cudaMemcpyHostToDevice);
    if (err != cudaSuccess) {
        printf("❌ 区间长度复制失败: %s\n", cudaGetErrorString(err));
        // 释放所有已分配的内存
        cudaFree(dev_candidate_table);
        cudaFree(dev_final_table);
        cudaFree(dev_dp_counter);
        cudaFree(dev_interval_start);
        cudaFree(dev_interval_length);
        return false;
    }
    
    // 🔧 关键修复：分配并复制动态步长数据到GPU
    size_t step_data_size = NUM_STEPS * 4 * sizeof(uint64_t);  // 每个步长4个uint64_t
    
    // 分配GPU内存
    err = cudaMalloc(&dev_step_exponents, step_data_size);
    if (err != cudaSuccess) {
        printf("❌ 步长指数GPU内存分配失败: %s\n", cudaGetErrorString(err));
        // 释放所有已分配的内存
        cudaFree(dev_candidate_table);
        cudaFree(dev_final_table);
        cudaFree(dev_dp_counter);
        cudaFree(dev_interval_start);
        cudaFree(dev_interval_length);
        return false;
    }
    
    err = cudaMalloc(&dev_step_points_x, step_data_size);
    if (err != cudaSuccess) {
        printf("❌ 步长点x坐标GPU内存分配失败: %s\n", cudaGetErrorString(err));
        // 释放所有已分配的内存
        cudaFree(dev_candidate_table);
        cudaFree(dev_final_table);
        cudaFree(dev_dp_counter);
        cudaFree(dev_interval_start);
        cudaFree(dev_interval_length);
        cudaFree(dev_step_exponents);
        return false;
    }
    
    err = cudaMalloc(&dev_step_points_y, step_data_size);
    if (err != cudaSuccess) {
        printf("❌ 步长点y坐标GPU内存分配失败: %s\n", cudaGetErrorString(err));
        // 释放所有已分配的内存
        cudaFree(dev_candidate_table);
        cudaFree(dev_final_table);
        cudaFree(dev_dp_counter);
        cudaFree(dev_interval_start);
        cudaFree(dev_interval_length);
        cudaFree(dev_step_exponents);
        cudaFree(dev_step_points_x);
        return false;
    }
    
    // 复制动态步长数据到GPU
    err = cudaMemcpy(dev_step_exponents, step_exponents, step_data_size, cudaMemcpyHostToDevice);
    if (err != cudaSuccess) {
        printf("❌ 步长指数复制失败: %s\n", cudaGetErrorString(err));
        // 释放所有已分配的内存
        cudaFree(dev_candidate_table);
        cudaFree(dev_final_table);
        cudaFree(dev_dp_counter);
        cudaFree(dev_interval_start);
        cudaFree(dev_interval_length);
        cudaFree(dev_step_exponents);
        cudaFree(dev_step_points_x);
        cudaFree(dev_step_points_y);
        return false;
    }
    
    err = cudaMemcpy(dev_step_points_x, step_points_x, step_data_size, cudaMemcpyHostToDevice);
    if (err != cudaSuccess) {
        printf("❌ 步长点x坐标复制失败: %s\n", cudaGetErrorString(err));
        // 释放所有已分配的内存
        cudaFree(dev_candidate_table);
        cudaFree(dev_final_table);
        cudaFree(dev_dp_counter);
        cudaFree(dev_interval_start);
        cudaFree(dev_interval_length);
        cudaFree(dev_step_exponents);
        cudaFree(dev_step_points_x);
        cudaFree(dev_step_points_y);
        return false;
    }
    
    err = cudaMemcpy(dev_step_points_y, step_points_y, step_data_size, cudaMemcpyHostToDevice);
    if (err != cudaSuccess) {
        printf("❌ 步长点y坐标复制失败: %s\n", cudaGetErrorString(err));
        // 释放所有已分配的内存
        cudaFree(dev_candidate_table);
        cudaFree(dev_final_table);
        cudaFree(dev_dp_counter);
        cudaFree(dev_interval_start);
        cudaFree(dev_interval_length);
        cudaFree(dev_step_exponents);
        cudaFree(dev_step_points_x);
        cudaFree(dev_step_points_y);
        return false;
    }
    
    printf("✅ 动态步长数据复制到GPU完成 (%.3f KB)\n",
           (3 * step_data_size) / 1024.0);
    
    // 清零内存
    cudaMemset(dev_candidate_table, 0, candidate_size);
    cudaMemset(dev_final_table, 0, final_size);
    cudaMemset(dev_dp_counter, 0, sizeof(uint32_t));
    
    printf("✅ GPU内存分配完成\n");
    printf("  候选表: %.2f MB\n", candidate_size / (1024.0 * 1024.0));
    printf("  最终表: %.2f MB\n", final_size / (1024.0 * 1024.0));
    
    return true;
}
```

## 7. 其他改进建议

1. **错误处理增强**：在关键操作后添加错误检查，确保资源正确释放。

2. **日志优化**：添加更详细的日志输出，便于调试和性能分析。

3. **参数验证**：在设置参数时添加验证逻辑，确保参数在合理范围内。

4. **内存优化**：对于大表，考虑使用内存映射文件或分块处理。

5. **并行化优化**：在CPU端权重计算部分使用多线程加速。

这些修改将使您的实现更符合Bernstein-Lange论文的要求，提高算法的正确性和效率。同时，改进的错误处理和内存管理将增强代码的健壮性。