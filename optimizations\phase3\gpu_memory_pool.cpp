#include "gpu_memory_pool.h"
#include <iostream>
#include <iomanip>
#include <algorithm>
#include <chrono>
#include <fstream>

// 静态成员初始化
std::unique_ptr<GPUMemoryPoolManager> GPUMemoryPoolManager::instance = nullptr;
std::mutex GPUMemoryPoolManager::instance_mutex;

GPUMemoryPool::GPUMemoryPool(int device_id, size_t max_size_gb) 
    : device_id(device_id), total_allocated(0), total_used(0), peak_usage(0),
      allocation_count(0), deallocation_count(0), reuse_count(0), next_allocation_id(1),
      max_pool_size(max_size_gb * 1024ULL * 1024 * 1024), min_block_size(1024),
      max_block_size(1024ULL * 1024 * 1024), fragmentation_threshold(0.3),
      auto_cleanup_enabled(true) {
    
    // 设置CUDA设备
    cudaSetDevice(device_id);
    
    std::cout << "GPUMemoryPool: Initialized for device " << device_id 
              << ", max size: " << max_size_gb << " GB" << std::endl;
}

GPUMemoryPool::~GPUMemoryPool() {
    reset();
}

void* GPUMemoryPool::allocate(size_t size, cudaStream_t stream) {
    if (size == 0) return nullptr;
    
    size = alignSize(size);
    
    std::lock_guard<std::mutex> lock(pool_mutex);
    
    // 查找合适的空闲块
    int block_index = findFreeBlock(size);
    
    if (block_index >= 0) {
        // 复用现有块
        auto& block = blocks[block_index];
        block->in_use = true;
        block->stream = stream;
        block->last_used = std::chrono::steady_clock::now();
        
        total_used.fetch_add(block->size);
        reuse_count.fetch_add(1);
        
        ptr_to_block_index[block->ptr] = block_index;
        
        return block->ptr;
    }
    
    // 创建新块
    block_index = createNewBlock(size, stream);
    if (block_index >= 0) {
        auto& block = blocks[block_index];
        total_used.fetch_add(block->size);
        allocation_count.fetch_add(1);
        
        ptr_to_block_index[block->ptr] = block_index;
        
        // 更新峰值使用量
        size_t current_used = total_used.load();
        size_t current_peak = peak_usage.load();
        while (current_used > current_peak && 
               !peak_usage.compare_exchange_weak(current_peak, current_used)) {
            current_peak = peak_usage.load();
        }
        
        return block->ptr;
    }
    
    return nullptr;
}

void GPUMemoryPool::deallocate(void* ptr) {
    if (!ptr) return;
    
    std::lock_guard<std::mutex> lock(pool_mutex);
    
    auto it = ptr_to_block_index.find(ptr);
    if (it == ptr_to_block_index.end()) {
        std::cerr << "GPUMemoryPool: Attempting to deallocate unknown pointer" << std::endl;
        return;
    }
    
    size_t block_index = it->second;
    if (block_index >= blocks.size()) {
        std::cerr << "GPUMemoryPool: Invalid block index" << std::endl;
        return;
    }
    
    auto& block = blocks[block_index];
    if (!block->in_use) {
        std::cerr << "GPUMemoryPool: Attempting to deallocate already free block" << std::endl;
        return;
    }
    
    block->in_use = false;
    block->stream = 0;
    block->last_used = std::chrono::steady_clock::now();
    
    total_used.fetch_sub(block->size);
    deallocation_count.fetch_add(1);
    
    ptr_to_block_index.erase(it);
    
    // 检查是否需要自动清理
    if (auto_cleanup_enabled && needsCleanup()) {
        cleanupExpiredBlocks();
    }
}

void* GPUMemoryPool::allocateAsync(size_t size, cudaStream_t stream) {
    // 对于异步分配，我们使用相同的逻辑但记录流信息
    return allocate(size, stream);
}

void GPUMemoryPool::deallocateAsync(void* ptr, cudaStream_t stream) {
    // 对于异步释放，我们可以延迟到流完成
    // 这里简化为直接释放
    deallocate(ptr);
}

int GPUMemoryPool::findFreeBlock(size_t size) {
    // 查找最适合的空闲块 (Best Fit算法)
    int best_index = -1;
    size_t best_size = SIZE_MAX;
    
    for (size_t i = 0; i < blocks.size(); i++) {
        auto& block = blocks[i];
        if (!block->in_use && block->size >= size && block->size < best_size) {
            best_index = i;
            best_size = block->size;
            
            // 如果找到完全匹配的块，直接返回
            if (block->size == size) {
                break;
            }
        }
    }
    
    return best_index;
}

int GPUMemoryPool::createNewBlock(size_t size, cudaStream_t stream) {
    // 检查是否超过最大池大小
    if (total_allocated.load() + size > max_pool_size) {
        std::cerr << "GPUMemoryPool: Would exceed maximum pool size" << std::endl;
        return -1;
    }
    
    // 分配GPU内存
    void* ptr;
    cudaError_t error = cudaMalloc(&ptr, size);
    if (error != cudaSuccess) {
        std::cerr << "GPUMemoryPool: cudaMalloc failed: " << cudaGetErrorString(error) << std::endl;
        return -1;
    }
    
    // 创建新的内存块
    auto block = std::make_unique<MemoryBlock>();
    block->ptr = ptr;
    block->size = size;
    block->in_use = true;
    block->stream = stream;
    block->device_id = device_id;
    block->allocation_id = next_allocation_id.fetch_add(1);
    block->last_used = std::chrono::steady_clock::now();
    
    blocks.push_back(std::move(block));
    total_allocated.fetch_add(size);
    
    return blocks.size() - 1;
}

size_t GPUMemoryPool::alignSize(size_t size) const {
    // 对齐到256字节边界以提高性能
    const size_t alignment = 256;
    return (size + alignment - 1) & ~(alignment - 1);
}

void GPUMemoryPool::cleanup(bool force) {
    std::lock_guard<std::mutex> lock(pool_mutex);
    
    auto now = std::chrono::steady_clock::now();
    auto expiry_time = std::chrono::minutes(5);  // 5分钟过期时间
    
    for (auto it = blocks.begin(); it != blocks.end();) {
        auto& block = *it;
        
        bool should_remove = force || 
            (!block->in_use && 
             (now - block->last_used) > expiry_time);
        
        if (should_remove) {
            if (block->ptr) {
                cudaFree(block->ptr);
                total_allocated.fetch_sub(block->size);
            }
            
            // 从快速查找表中移除
            auto ptr_it = ptr_to_block_index.find(block->ptr);
            if (ptr_it != ptr_to_block_index.end()) {
                ptr_to_block_index.erase(ptr_it);
            }
            
            it = blocks.erase(it);
        } else {
            ++it;
        }
    }
    
    // 重建快速查找表索引
    ptr_to_block_index.clear();
    for (size_t i = 0; i < blocks.size(); i++) {
        if (blocks[i]->in_use) {
            ptr_to_block_index[blocks[i]->ptr] = i;
        }
    }
}

void GPUMemoryPool::cleanupExpiredBlocks() {
    cleanup(false);
}

bool GPUMemoryPool::needsCleanup() const {
    // 如果碎片率过高或空闲块过多，需要清理
    double fragmentation = calculateFragmentation();
    return fragmentation > fragmentation_threshold;
}

double GPUMemoryPool::calculateFragmentation() const {
    if (blocks.empty()) return 0.0;
    
    size_t free_blocks = 0;
    size_t total_blocks = blocks.size();
    
    for (const auto& block : blocks) {
        if (!block->in_use) {
            free_blocks++;
        }
    }
    
    return (double)free_blocks / (double)total_blocks;
}

MemoryPoolStatistics GPUMemoryPool::getStatistics() const {
    MemoryPoolStatistics stats;
    
    stats.total_allocated = total_allocated.load();
    stats.total_used = total_used.load();
    stats.peak_usage = peak_usage.load();
    stats.allocation_count = allocation_count.load();
    stats.deallocation_count = deallocation_count.load();
    stats.reuse_count = reuse_count.load();
    stats.fragmentation_ratio = calculateFragmentation();
    
    if (stats.allocation_count > 0) {
        stats.reuse_ratio = (double)stats.reuse_count / (double)stats.allocation_count;
    }
    
    return stats;
}

void GPUMemoryPool::printStatistics() const {
    auto stats = getStatistics();
    
    std::cout << "=== GPU Memory Pool Statistics (Device " << device_id << ") ===" << std::endl;
    std::cout << "Total allocated: " << (stats.total_allocated / (1024*1024)) << " MB" << std::endl;
    std::cout << "Currently used: " << (stats.total_used / (1024*1024)) << " MB" << std::endl;
    std::cout << "Peak usage: " << (stats.peak_usage / (1024*1024)) << " MB" << std::endl;
    std::cout << "Total blocks: " << blocks.size() << std::endl;
    std::cout << "Allocations: " << stats.allocation_count << std::endl;
    std::cout << "Deallocations: " << stats.deallocation_count << std::endl;
    std::cout << "Reuses: " << stats.reuse_count << std::endl;
    std::cout << "Reuse ratio: " << std::fixed << std::setprecision(2) 
              << (stats.reuse_ratio * 100.0) << "%" << std::endl;
    std::cout << "Fragmentation: " << std::fixed << std::setprecision(2) 
              << (stats.fragmentation_ratio * 100.0) << "%" << std::endl;
}

void GPUMemoryPool::reset() {
    std::lock_guard<std::mutex> lock(pool_mutex);
    
    // 释放所有GPU内存
    for (auto& block : blocks) {
        if (block->ptr) {
            cudaFree(block->ptr);
        }
    }
    
    blocks.clear();
    ptr_to_block_index.clear();
    
    total_allocated.store(0);
    total_used.store(0);
    peak_usage.store(0);
    allocation_count.store(0);
    deallocation_count.store(0);
    reuse_count.store(0);
    next_allocation_id.store(1);
}

size_t GPUMemoryPool::getAvailableMemory() const {
    size_t free_mem, total_mem;
    cudaMemGetInfo(&free_mem, &total_mem);
    return free_mem;
}

// GPUMemoryPoolManager实现
GPUMemoryPoolManager& GPUMemoryPoolManager::getInstance() {
    std::lock_guard<std::mutex> lock(instance_mutex);
    if (!instance) {
        instance = std::unique_ptr<GPUMemoryPoolManager>(new GPUMemoryPoolManager());
    }
    return *instance;
}

GPUMemoryPool* GPUMemoryPoolManager::getPool(int device_id) {
    std::lock_guard<std::mutex> lock(pools_mutex);
    
    auto it = pools.find(device_id);
    if (it == pools.end()) {
        // 自动创建内存池
        createPool(device_id);
        it = pools.find(device_id);
    }
    
    return (it != pools.end()) ? it->second.get() : nullptr;
}

bool GPUMemoryPoolManager::createPool(int device_id, size_t max_size_gb) {
    std::lock_guard<std::mutex> lock(pools_mutex);
    
    if (pools.find(device_id) != pools.end()) {
        return true;  // 已存在
    }
    
    try {
        auto pool = std::make_unique<GPUMemoryPool>(device_id, max_size_gb);
        pools[device_id] = std::move(pool);
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Failed to create memory pool for device " << device_id 
                  << ": " << e.what() << std::endl;
        return false;
    }
}

void GPUMemoryPoolManager::destroyPool(int device_id) {
    std::lock_guard<std::mutex> lock(pools_mutex);
    pools.erase(device_id);
}

void GPUMemoryPoolManager::destroyAllPools() {
    std::lock_guard<std::mutex> lock(pools_mutex);
    pools.clear();
}

void GPUMemoryPoolManager::printAllPoolsStatistics() const {
    std::lock_guard<std::mutex> lock(const_cast<std::mutex&>(pools_mutex));
    
    for (const auto& pair : pools) {
        pair.second->printStatistics();
        std::cout << std::endl;
    }
}

size_t GPUMemoryPoolManager::getTotalMemoryUsage() const {
    std::lock_guard<std::mutex> lock(const_cast<std::mutex&>(pools_mutex));
    
    size_t total = 0;
    for (const auto& pair : pools) {
        total += pair.second->getTotalUsed();
    }
    
    return total;
}
