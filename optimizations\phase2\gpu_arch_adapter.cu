#include "gpu_arch_adapter.h"
#include <iostream>
#include <iomanip>
#include <algorithm>
#include <cmath>

// 静态成员初始化
std::vector<GPUArchConfig> GPUArchAdapter::arch_configs;
bool GPUArchAdapter::configs_initialized = false;

void GPUArchAdapter::initializeConfigs() {
    if (configs_initialized) return;
    
    arch_configs.clear();
    
    // Maxwell 2.0 (SM 5.2)
    arch_configs.push_back(createMaxwellConfig(2));
    
    // Pascal (SM 6.0, 6.1)
    arch_configs.push_back(createPascalConfig(0));
    arch_configs.push_back(createPascalConfig(1));
    
    // Volta/Turing (SM 7.0, 7.5)
    arch_configs.push_back(createVoltaTuringConfig(0));
    arch_configs.push_back(createVoltaTuringConfig(5));
    
    // Ampere/Ada (SM 8.0, 8.6, 8.9)
    arch_configs.push_back(createAmpereAdaConfig(0));
    arch_configs.push_back(createAmpereAdaConfig(6));
    arch_configs.push_back(createAmpereAdaConfig(9));
    
    // Hopper (SM 9.0)
    arch_configs.push_back(createHopperConfig(0));
    
    configs_initialized = true;
    
    std::cout << "GPUArchAdapter: Initialized " << arch_configs.size() 
              << " GPU architecture configurations" << std::endl;
}

GPUArchConfig GPUArchAdapter::getOptimalConfig(int device_id) {
    if (!configs_initialized) {
        initializeConfigs();
    }
    
    cudaDeviceProp prop;
    cudaError_t error = cudaGetDeviceProperties(&prop, device_id);
    if (error != cudaSuccess) {
        std::cerr << "Failed to get GPU properties for device " << device_id << std::endl;
        return createVoltaTuringConfig(5);  // 默认配置
    }
    
    // 查找匹配的配置
    for (const auto& config : arch_configs) {
        if (config.major == prop.major && config.minor == prop.minor) {
            return config;
        }
    }
    
    // 如果没有精确匹配，创建新配置
    return createArchConfig(prop.major, prop.minor);
}

GPUArchConfig GPUArchAdapter::createArchConfig(int major, int minor) {
    switch (major) {
        case 5: return createMaxwellConfig(minor);
        case 6: return createPascalConfig(minor);
        case 7: return createVoltaTuringConfig(minor);
        case 8: return createAmpereAdaConfig(minor);
        case 9: return createHopperConfig(minor);
        default:
            std::cerr << "Unsupported GPU architecture: SM " << major << "." << minor << std::endl;
            return createVoltaTuringConfig(5);  // 默认配置
    }
}

GPUArchConfig GPUArchAdapter::createMaxwellConfig(int minor) {
    GPUArchConfig config;
    config.major = 5;
    config.minor = minor;
    config.arch_name = "Maxwell 2.0";
    
    // 硬件特性
    config.max_blocks_per_sm = 32;
    config.max_threads_per_block = 1024;
    config.max_threads_per_sm = 2048;
    config.shared_memory_per_block = 48 * 1024;
    config.shared_memory_per_sm = 64 * 1024;
    config.warp_size = 32;
    
    // 保守的优化配置
    config.optimal_blocks_per_sm = 2;
    config.optimal_threads_per_block = 256;
    config.kangaroos_per_sm = 512;
    config.kangaroos_per_thread = 2;
    config.shared_memory_usage = 16 * 1024;
    
    // 特性支持
    config.supports_cooperative_groups = false;
    config.supports_unified_memory = false;
    config.supports_cuda_graphs = false;
    config.supports_async_memory = false;
    
    // 性能调优
    config.optimal_dp_adjustment = +2;  // 增加DP位数减少内存压力
    config.memory_usage_ratio = 0.6;
    config.steps_per_kernel = 512;
    
    return config;
}

GPUArchConfig GPUArchAdapter::createPascalConfig(int minor) {
    GPUArchConfig config;
    config.major = 6;
    config.minor = minor;
    config.arch_name = "Pascal";
    
    // 硬件特性
    config.max_blocks_per_sm = 32;
    config.max_threads_per_block = 1024;
    config.max_threads_per_sm = 2048;
    config.shared_memory_per_block = 48 * 1024;
    config.shared_memory_per_sm = 64 * 1024;
    config.warp_size = 32;
    
    // 平衡的优化配置
    config.optimal_blocks_per_sm = 3;
    config.optimal_threads_per_block = 512;
    config.kangaroos_per_sm = 1024;
    config.kangaroos_per_thread = 2;
    config.shared_memory_usage = 24 * 1024;
    
    // 特性支持
    config.supports_cooperative_groups = true;
    config.supports_unified_memory = true;
    config.supports_cuda_graphs = false;
    config.supports_async_memory = true;
    
    // 性能调优
    config.optimal_dp_adjustment = +1;
    config.memory_usage_ratio = 0.7;
    config.steps_per_kernel = 768;
    
    return config;
}

GPUArchConfig GPUArchAdapter::createVoltaTuringConfig(int minor) {
    GPUArchConfig config;
    config.major = 7;
    config.minor = minor;
    config.arch_name = (minor == 0) ? "Volta" : "Turing";
    
    // 硬件特性
    config.max_blocks_per_sm = 32;
    config.max_threads_per_block = 1024;
    config.max_threads_per_sm = 2048;
    config.shared_memory_per_block = 48 * 1024;
    config.shared_memory_per_sm = 96 * 1024;  // Volta/Turing增加了共享内存
    config.warp_size = 32;
    
    // 激进的优化配置
    config.optimal_blocks_per_sm = 4;
    config.optimal_threads_per_block = 512;
    config.kangaroos_per_sm = 1536;
    config.kangaroos_per_thread = 3;
    config.shared_memory_usage = 32 * 1024;
    
    // 特性支持
    config.supports_cooperative_groups = true;
    config.supports_unified_memory = true;
    config.supports_cuda_graphs = true;
    config.supports_async_memory = true;
    
    // 性能调优
    config.optimal_dp_adjustment = 0;  // 平衡配置
    config.memory_usage_ratio = 0.75;
    config.steps_per_kernel = 1024;
    
    return config;
}

GPUArchConfig GPUArchAdapter::createAmpereAdaConfig(int minor) {
    GPUArchConfig config;
    config.major = 8;
    config.minor = minor;
    
    if (minor == 9) {
        config.arch_name = "Ada Lovelace";
    } else {
        config.arch_name = "Ampere";
    }
    
    // 硬件特性
    config.max_blocks_per_sm = 32;
    config.max_threads_per_block = 1024;
    config.max_threads_per_sm = 2048;
    config.shared_memory_per_block = 48 * 1024;
    config.shared_memory_per_sm = 100 * 1024;  // Ampere进一步增加
    config.warp_size = 32;
    
    // 最激进的优化配置
    config.optimal_blocks_per_sm = 6;
    config.optimal_threads_per_block = 1024;
    config.kangaroos_per_sm = 2048;
    config.kangaroos_per_thread = 2;
    config.shared_memory_usage = 48 * 1024;
    
    // 特性支持
    config.supports_cooperative_groups = true;
    config.supports_unified_memory = true;
    config.supports_cuda_graphs = true;
    config.supports_async_memory = true;
    
    // 性能调优
    config.optimal_dp_adjustment = -1;  // 可以处理更多DP
    config.memory_usage_ratio = 0.85;
    config.steps_per_kernel = 1024;
    
    return config;
}

GPUArchConfig GPUArchAdapter::createHopperConfig(int minor) {
    GPUArchConfig config;
    config.major = 9;
    config.minor = minor;
    config.arch_name = "Hopper";
    
    // 硬件特性 (基于H100)
    config.max_blocks_per_sm = 32;
    config.max_threads_per_block = 1024;
    config.max_threads_per_sm = 2048;
    config.shared_memory_per_block = 48 * 1024;
    config.shared_memory_per_sm = 228 * 1024;  // Hopper大幅增加
    config.warp_size = 32;
    
    // 极限优化配置
    config.optimal_blocks_per_sm = 8;
    config.optimal_threads_per_block = 1024;
    config.kangaroos_per_sm = 4096;
    config.kangaroos_per_thread = 4;
    config.shared_memory_usage = 64 * 1024;
    
    // 特性支持
    config.supports_cooperative_groups = true;
    config.supports_unified_memory = true;
    config.supports_cuda_graphs = true;
    config.supports_async_memory = true;
    
    // 性能调优
    config.optimal_dp_adjustment = -2;  // 激进配置
    config.memory_usage_ratio = 0.9;
    config.steps_per_kernel = 2048;
    
    return config;
}

KernelLaunchConfig GPUArchAdapter::calculateKernelConfig(const GPUArchConfig& config,
                                                        uint32_t total_kangaroos,
                                                        size_t available_memory) {
    KernelLaunchConfig launch_config;
    
    // 计算SM数量 (需要查询实际硬件)
    int device_id;
    cudaGetDevice(&device_id);
    cudaDeviceProp prop;
    cudaGetDeviceProperties(&prop, device_id);
    
    uint32_t sm_count = prop.multiProcessorCount;
    
    // 计算每SM的袋鼠数
    uint32_t kangaroos_per_sm = std::min(config.kangaroos_per_sm, 
                                        total_kangaroos / sm_count + 1);
    
    // 计算块配置
    launch_config.block_size = dim3(config.optimal_threads_per_block, 1, 1);
    
    // 计算网格配置 (每SM一个或多个块)
    uint32_t blocks_per_sm = config.optimal_blocks_per_sm;
    launch_config.grid_size = dim3(sm_count * blocks_per_sm, 1, 1);
    
    // 共享内存配置
    launch_config.shared_memory_size = config.shared_memory_usage;
    
    // 其他配置
    launch_config.stream = 0;  // 默认流
    launch_config.total_kangaroos = total_kangaroos;
    launch_config.kangaroos_per_block = kangaroos_per_sm / blocks_per_sm;
    
    return launch_config;
}

uint32_t GPUArchAdapter::calculateOptimalKangaroos(int device_id, 
                                                  size_t available_memory,
                                                  uint32_t range_bits) {
    GPUArchConfig config = getOptimalConfig(device_id);
    
    // 基于内存限制计算
    size_t kangaroo_memory_per_item = 100;  // 每个袋鼠约100字节
    uint32_t memory_limited = static_cast<uint32_t>(available_memory / kangaroo_memory_per_item);
    
    // 基于GPU架构计算
    cudaDeviceProp prop;
    cudaGetDeviceProperties(&prop, device_id);
    uint32_t arch_optimal = prop.multiProcessorCount * config.kangaroos_per_sm;
    
    // 基于搜索范围计算理论最优
    double range_size = pow(2.0, (double)range_bits);
    uint32_t theory_optimal = (uint32_t)sqrt(range_size / 1000.0);  // 启发式
    
    // 取最小值确保稳定性
    uint32_t optimal = std::min({memory_limited, arch_optimal, theory_optimal});
    
    // 确保合理范围
    optimal = std::max(1024u, std::min(optimal, 10000000u));
    
    std::cout << "Optimal kangaroo calculation:" << std::endl;
    std::cout << "  Memory limited: " << memory_limited << std::endl;
    std::cout << "  Architecture optimal: " << arch_optimal << std::endl;
    std::cout << "  Theory optimal: " << theory_optimal << std::endl;
    std::cout << "  Final optimal: " << optimal << std::endl;
    
    return optimal;
}

void GPUArchAdapter::printArchConfig(const GPUArchConfig& config) {
    std::cout << "=== GPU Architecture Configuration ===" << std::endl;
    std::cout << "Architecture: " << config.arch_name 
              << " (SM " << config.major << "." << config.minor << ")" << std::endl;
    std::cout << std::endl;
    
    std::cout << "Hardware Limits:" << std::endl;
    std::cout << "  Max blocks/SM: " << config.max_blocks_per_sm << std::endl;
    std::cout << "  Max threads/block: " << config.max_threads_per_block << std::endl;
    std::cout << "  Max threads/SM: " << config.max_threads_per_sm << std::endl;
    std::cout << "  Shared memory/block: " << (config.shared_memory_per_block / 1024) << " KB" << std::endl;
    std::cout << "  Shared memory/SM: " << (config.shared_memory_per_sm / 1024) << " KB" << std::endl;
    std::cout << std::endl;
    
    std::cout << "Optimal Configuration:" << std::endl;
    std::cout << "  Blocks/SM: " << config.optimal_blocks_per_sm << std::endl;
    std::cout << "  Threads/block: " << config.optimal_threads_per_block << std::endl;
    std::cout << "  Kangaroos/SM: " << config.kangaroos_per_sm << std::endl;
    std::cout << "  Kangaroos/thread: " << config.kangaroos_per_thread << std::endl;
    std::cout << "  Shared memory usage: " << (config.shared_memory_usage / 1024) << " KB" << std::endl;
    std::cout << std::endl;
    
    std::cout << "Feature Support:" << std::endl;
    std::cout << "  Cooperative Groups: " << (config.supports_cooperative_groups ? "Yes" : "No") << std::endl;
    std::cout << "  Unified Memory: " << (config.supports_unified_memory ? "Yes" : "No") << std::endl;
    std::cout << "  CUDA Graphs: " << (config.supports_cuda_graphs ? "Yes" : "No") << std::endl;
    std::cout << "  Async Memory: " << (config.supports_async_memory ? "Yes" : "No") << std::endl;
}

void GPUArchAdapter::printKernelConfig(const KernelLaunchConfig& config) {
    std::cout << "=== Kernel Launch Configuration ===" << std::endl;
    std::cout << "Grid size: (" << config.grid_size.x << ", " 
              << config.grid_size.y << ", " << config.grid_size.z << ")" << std::endl;
    std::cout << "Block size: (" << config.block_size.x << ", " 
              << config.block_size.y << ", " << config.block_size.z << ")" << std::endl;
    std::cout << "Shared memory: " << (config.shared_memory_size / 1024) << " KB" << std::endl;
    std::cout << "Total kangaroos: " << config.total_kangaroos << std::endl;
    std::cout << "Kangaroos/block: " << config.kangaroos_per_block << std::endl;
}

std::string GPUArchAdapter::getArchitectureName(int major, int minor) {
    switch (major) {
        case 5: return (minor == 2) ? "Maxwell 2.0" : "Maxwell";
        case 6: return "Pascal";
        case 7: return (minor == 0) ? "Volta" : "Turing";
        case 8: return (minor == 9) ? "Ada Lovelace" : "Ampere";
        case 9: return "Hopper";
        default: return "Unknown";
    }
}

// GPUPerformanceMonitor实现
GPUPerformanceMonitor::GPUPerformanceMonitor() 
    : last_kernel_time(0.0f), total_operations(0), total_time_ms(0) {
    cudaEventCreate(&start_event);
    cudaEventCreate(&stop_event);
}

GPUPerformanceMonitor::~GPUPerformanceMonitor() {
    cudaEventDestroy(start_event);
    cudaEventDestroy(stop_event);
}

void GPUPerformanceMonitor::startMeasurement() {
    cudaEventRecord(start_event);
}

float GPUPerformanceMonitor::endMeasurement() {
    cudaEventRecord(stop_event);
    cudaEventSynchronize(stop_event);
    cudaEventElapsedTime(&last_kernel_time, start_event, stop_event);
    total_time_ms += (uint64_t)last_kernel_time;
    return last_kernel_time;
}

void GPUPerformanceMonitor::updateOperations(uint64_t operations) {
    total_operations += operations;
}

double GPUPerformanceMonitor::getAveragePerformance() const {
    if (total_time_ms == 0) return 0.0;
    return (double)total_operations / ((double)total_time_ms / 1000.0);
}

void GPUPerformanceMonitor::reset() {
    total_operations = 0;
    total_time_ms = 0;
    last_kernel_time = 0.0f;
}
