Identity=..\optimizations\phase2\gpu_arch_adapter.cu
AdditionalCompilerOptions=
AdditionalCompilerOptions=
AdditionalDependencies=
AdditionalDeps=
AdditionalLibraryDirectories=
AdditionalOptions= -std=c++17 --generate-code=arch=compute_52,code=[compute_52,sm_52] --generate-code=arch=compute_61,code=[compute_61,sm_61] --generate-code=arch=compute_70,code=[compute_70,sm_70] --generate-code=arch=compute_75,code=[compute_75,sm_75] --generate-code=arch=compute_80,code=[compute_80,sm_80] --generate-code=arch=compute_86,code=[compute_86,sm_86] --generate-code=arch=compute_89,code=[compute_89,sm_89] --generate-code=arch=compute_90,code=[compute_90,sm_90] -Xptxas=-v -O3 --expt-relaxed-constexpr --expt-extended-lambda -lineinfo -Xcompiler="/EHsc -Ob2"
AdditionalOptions= -std=c++17 --generate-code=arch=compute_52,code=[compute_52,sm_52] --generate-code=arch=compute_61,code=[compute_61,sm_61] --generate-code=arch=compute_70,code=[compute_70,sm_70] --generate-code=arch=compute_75,code=[compute_75,sm_75] --generate-code=arch=compute_80,code=[compute_80,sm_80] --generate-code=arch=compute_86,code=[compute_86,sm_86] --generate-code=arch=compute_89,code=[compute_89,sm_89] --generate-code=arch=compute_90,code=[compute_90,sm_90] -Xptxas=-v -O3 --expt-relaxed-constexpr --expt-extended-lambda -lineinfo -Xcompiler="/EHsc -Ob2"
CodeGeneration=
CodeGeneration=
CompileOut=kangaroo.dir\Release\gpu_arch_adapter.obj
CudaRuntime=Static
CudaToolkitCustomDir=
DebugInformationFormat=None
DebugInformationFormat=None
Defines=;_WINDOWS;NDEBUG;WIN64;_CRT_SECURE_NO_WARNINGS;WITHGPU;USE_MODERN_CUDA;AUTO_GPU_DETECT;CMAKE_INTDIR="Release";_MBCS;;WIN32;_WINDOWS;NDEBUG;WIN64;_CRT_SECURE_NO_WARNINGS;WITHGPU;USE_MODERN_CUDA;AUTO_GPU_DETECT;CMAKE_INTDIR="Release"
Emulation=false
EnableVirtualArchInFatbin=true
ExtensibleWholeProgramCompilation=false
FastMath=true
GenerateLineInfo=false
GenerateRelocatableDeviceCode=true
GPUDebugInfo=false
GPUDebugInfo=false
HostDebugInfo=false
Include=D:\mybitcoin\2\cuda-bsgs-production-full\Kangaroo\.;D:\mybitcoin\2\cuda-bsgs-production-full\Kangaroo\SECPK1;D:\mybitcoin\2\cuda-bsgs-production-full\Kangaroo\GPU;D:\mybitcoin\2\cuda-bsgs-production-full\Kangaroo\optimizations\phase1;D:\mybitcoin\2\cuda-bsgs-production-full\Kangaroo\optimizations\phase2;D:\mybitcoin\2\cuda-bsgs-production-full\Kangaroo\optimizations\phase3;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\include
Inputs=
InterleaveSourceInPTX=false
Keep=false
KeepDir=kangaroo\x64\Release
LinkOut=
MaxRegCount=0
NvccCompilation=compile
NvccPath=
Optimization=O2
Optimization=O2
PerformDeviceLink=
PerformDeviceLinkTimeOptimization=
PtxAsOptionV=false
RequiredIncludes=
Runtime=MD
Runtime=MD
RuntimeChecks=Default
RuntimeChecks=Default
SplitCompile=Default
SplitCompileCustomThreads=
TargetMachinePlatform=64
TargetMachinePlatform=64
TypeInfo=true
TypeInfo=true
UseHostDefines=true
UseHostInclude=false
UseHostLibraryDependencies=
UseHostLibraryDirectories=
Warning=W3
Warning=W3
