#include "gpu_detector.h"
#include <iostream>
#include <algorithm>
#include <iomanip>
#include <cmath>

std::vector<GPUInfo> GPUDetector::detectAllGPUs() {
    std::vector<GPUInfo> gpus;
    int device_count;
    
    cudaError_t error = cudaGetDeviceCount(&device_count);
    if (error != cudaSuccess) {
        std::cerr << "CUDA error: " << cudaGetErrorString(error) << std::endl;
        return gpus;
    }
    
    for (int i = 0; i < device_count; i++) {
        cudaDeviceProp prop;
        error = cudaGetDeviceProperties(&prop, i);
        if (error != cudaSuccess) {
            std::cerr << "Failed to get properties for device " << i << std::endl;
            continue;
        }
        
        GPUInfo info;
        info.device_id = i;
        info.name = prop.name;
        info.major = prop.major;
        info.minor = prop.minor;
        info.total_memory = prop.totalGlobalMem;
        info.multiprocessor_count = prop.multiProcessorCount;
        info.max_threads_per_block = prop.maxThreadsPerBlock;
        info.max_threads_per_multiprocessor = prop.maxThreadsPerMultiProcessor;
        info.warp_size = prop.warpSize;
        info.shared_memory_per_block = prop.sharedMemPerBlock;
        info.shared_memory_per_multiprocessor = prop.sharedMemPerMultiprocessor;
        info.max_blocks_per_multiprocessor = prop.maxBlocksPerMultiProcessor;
        info.memory_clock_rate = prop.memoryClockRate;
        info.memory_bus_width = prop.memoryBusWidth;
        info.core_clock_rate = prop.clockRate;
        
        // 获取可用内存
        size_t free_mem, total_mem;
        cudaSetDevice(i);
        cudaMemGetInfo(&free_mem, &total_mem);
        info.free_memory = free_mem;
        
        // 填充高级特性信息
        fillAdvancedInfo(info, i);
        
        // 计算内存带宽
        info.memory_bandwidth = calculateMemoryBandwidth(info);
        
        gpus.push_back(info);
    }
    
    return gpus;
}

void GPUDetector::fillAdvancedInfo(GPUInfo& info, int device_id) {
    // 检查高级特性支持
    info.supports_cooperative_groups = (info.major >= 6);
    info.supports_unified_memory = (info.major >= 6);
    info.supports_cuda_graphs = (info.major >= 7);
    info.supports_async_memory = (info.major >= 6);
    
    // 更精确的特性检测
    if (info.major >= 7) {
        // Volta及以上架构的特殊特性
        info.supports_cooperative_groups = true;
    }
    
    if (info.major >= 8) {
        // Ampere及以上架构的特殊特性
        info.supports_async_memory = true;
    }
}

double GPUDetector::calculateMemoryBandwidth(const GPUInfo& info) {
    // 内存带宽 = 内存时钟频率 × 内存总线宽度 × 2 / 8 / 1000
    // 结果单位: GB/s
    return (double)info.memory_clock_rate * info.memory_bus_width * 2.0 / 8.0 / 1000.0;
}

GPUInfo GPUDetector::getBestGPU() {
    auto gpus = detectAllGPUs();
    if (gpus.empty()) {
        throw std::runtime_error("No CUDA-capable GPU found");
    }
    
    // 选择性能评分最高的GPU
    auto best = std::max_element(gpus.begin(), gpus.end(), 
        [](const GPUInfo& a, const GPUInfo& b) {
            return calculatePerformanceScore(a) < calculatePerformanceScore(b);
        });
    
    return *best;
}

double GPUDetector::calculatePerformanceScore(const GPUInfo& info) {
    if (!isArchitectureSupported(info.major, info.minor)) {
        return 0.0;
    }
    
    // 性能评分算法
    double compute_score = info.major * 1000 + info.minor * 100;  // 计算能力权重
    double memory_score = (info.total_memory / (1024.0 * 1024 * 1024)) * 10;  // 内存大小权重
    double sm_score = info.multiprocessor_count * 5;  // SM数量权重
    double bandwidth_score = info.memory_bandwidth;  // 内存带宽权重
    
    return compute_score + memory_score + sm_score + bandwidth_score;
}

bool GPUDetector::isArchitectureSupported(int major, int minor) {
    // 支持Maxwell 2.0及以上架构 (SM 5.2+)
    if (major < 5) return false;
    if (major == 5 && minor < 2) return false;
    if (major >= 10) return false;  // 未来架构预留
    
    return true;
}

std::string GPUDetector::getArchitectureName(int major, int minor) {
    switch (major) {
        case 5:
            if (minor == 2) return "Maxwell 2.0";
            return "Maxwell";
        case 6:
            if (minor == 0) return "Pascal";
            if (minor == 1) return "Pascal";
            return "Pascal";
        case 7:
            if (minor == 0) return "Volta";
            if (minor == 5) return "Turing";
            return "Volta/Turing";
        case 8:
            if (minor == 0) return "Ampere";
            if (minor == 6) return "Ampere";
            if (minor == 9) return "Ada Lovelace";
            return "Ampere/Ada";
        case 9:
            if (minor == 0) return "Hopper";
            return "Hopper";
        default:
            return "Unknown";
    }
}

KangarooGPUConfig GPUDetector::getRecommendedConfig(const GPUInfo& info) {
    KangarooGPUConfig config;
    
    // 基于GPU架构的推荐配置
    if (info.major <= 6) {
        // Pascal及以下: 保守配置
        config.blocks_per_sm = 2;
        config.threads_per_block = 256;
        config.kangaroos_per_sm = 512;
        config.shared_memory_usage = 16 * 1024;  // 16KB
        config.optimal_dp_bits = 22;
        config.use_cooperative_groups = false;
        config.use_async_memory = false;
        config.memory_usage_ratio = 0.6;
    } else if (info.major == 7) {
        // Volta/Turing: 平衡配置
        config.blocks_per_sm = 4;
        config.threads_per_block = 512;
        config.kangaroos_per_sm = 1024;
        config.shared_memory_usage = 32 * 1024;  // 32KB
        config.optimal_dp_bits = 20;
        config.use_cooperative_groups = true;
        config.use_async_memory = true;
        config.memory_usage_ratio = 0.75;
    } else {
        // Ampere及以上: 激进配置
        config.blocks_per_sm = 6;
        config.threads_per_block = 1024;
        config.kangaroos_per_sm = 2048;
        config.shared_memory_usage = 64 * 1024;  // 64KB
        config.optimal_dp_bits = 18;
        config.use_cooperative_groups = true;
        config.use_async_memory = true;
        config.memory_usage_ratio = 0.85;
    }
    
    // 根据实际硬件限制调整
    config.threads_per_block = std::min(config.threads_per_block, 
                                       (uint32_t)info.max_threads_per_block);
    config.shared_memory_usage = std::min(config.shared_memory_usage,
                                         (uint32_t)info.shared_memory_per_block);
    
    return config;
}

void GPUDetector::printGPUInfo(const GPUInfo& info) {
    std::cout << "GPU #" << info.device_id << ": " << info.name << std::endl;
    std::cout << "  Compute Capability: " << info.major << "." << info.minor 
              << " (" << getArchitectureName(info.major, info.minor) << ")" << std::endl;
    std::cout << "  Memory: " << (info.total_memory / (1024*1024)) << " MB total, "
              << (info.free_memory / (1024*1024)) << " MB free" << std::endl;
    std::cout << "  Multiprocessors: " << info.multiprocessor_count << std::endl;
    std::cout << "  Max Threads/Block: " << info.max_threads_per_block << std::endl;
    std::cout << "  Max Threads/SM: " << info.max_threads_per_multiprocessor << std::endl;
    std::cout << "  Shared Memory/Block: " << (info.shared_memory_per_block / 1024) << " KB" << std::endl;
    std::cout << "  Memory Bandwidth: " << std::fixed << std::setprecision(1) 
              << info.memory_bandwidth << " GB/s" << std::endl;
    std::cout << "  Performance Score: " << std::fixed << std::setprecision(1) 
              << calculatePerformanceScore(info) << std::endl;
    std::cout << "  Supported: " << (isArchitectureSupported(info.major, info.minor) ? "Yes" : "No") << std::endl;

    // 高级特性
    std::cout << "  Advanced Features:" << std::endl;
    std::cout << "    Cooperative Groups: " << (info.supports_cooperative_groups ? "Yes" : "No") << std::endl;
    std::cout << "    Unified Memory: " << (info.supports_unified_memory ? "Yes" : "No") << std::endl;
    std::cout << "    CUDA Graphs: " << (info.supports_cuda_graphs ? "Yes" : "No") << std::endl;
    std::cout << "    Async Memory: " << (info.supports_async_memory ? "Yes" : "No") << std::endl;
}

void GPUDetector::printAllGPUs() {
    auto gpus = detectAllGPUs();
    std::cout << "=== GPU Detection Results ===" << std::endl;
    std::cout << "Found " << gpus.size() << " CUDA-capable GPU(s)" << std::endl;
    std::cout << std::endl;
    
    for (const auto& gpu : gpus) {
        printGPUInfo(gpu);
        std::cout << std::endl;
    }
    
    if (!gpus.empty()) {
        auto best = getBestGPU();
        std::cout << "Best GPU: #" << best.device_id << " " << best.name
                  << " (Score: " << std::fixed << std::setprecision(1)
                  << calculatePerformanceScore(best) << ")" << std::endl;
    }
}

bool GPUDetector::checkCUDAEnvironment() {
    int runtime_version, driver_version;
    
    cudaError_t error = cudaRuntimeGetVersion(&runtime_version);
    if (error != cudaSuccess) {
        std::cerr << "Failed to get CUDA runtime version: " << cudaGetErrorString(error) << std::endl;
        return false;
    }
    
    error = cudaDriverGetVersion(&driver_version);
    if (error != cudaSuccess) {
        std::cerr << "Failed to get CUDA driver version: " << cudaGetErrorString(error) << std::endl;
        return false;
    }
    
    std::cout << "CUDA Runtime Version: " << (runtime_version / 1000) << "." << ((runtime_version % 100) / 10) << std::endl;
    std::cout << "CUDA Driver Version: " << (driver_version / 1000) << "." << ((driver_version % 100) / 10) << std::endl;
    
    return true;
}
