/**
 * @file sharded_hashtable.cu
 * @brief 分片哈希表实现 - Phase 3内存优化
 */

#include "sharded_hashtable.cuh"
#include <cuda_runtime.h>
#include <cstdio>
#include <cstdlib>

/**
 * @brief 构造函数
 */
ShardedHashTable_512::ShardedHashTable_512(int shards) 
    : num_shards(shards), total_capacity(0), total_count(0),
      dev_shards(nullptr), dev_entries_pool(nullptr), dev_occupancy_pool(nullptr) {
    
    // 限制分片数量
    if (num_shards > ShardedHashConfig::MAX_SHARDS) {
        num_shards = ShardedHashConfig::MAX_SHARDS;
    }
    if (num_shards < 1) {
        num_shards = 1;
    }
    
    // 分配主机内存
    shards = new HashShard_512[num_shards];
    
    // 初始化分片
    for (int i = 0; i < num_shards; i++) {
        shards[i].capacity = ShardedHashConfig::ENTRIES_PER_SHARD;
        shards[i].count = 0;
        shards[i].shard_id = i;
        shards[i].entries = nullptr;
        shards[i].occupancy = nullptr;
    }
    
    total_capacity = num_shards * ShardedHashConfig::ENTRIES_PER_SHARD;
    
    printf("[Sharded Hash] Initialized %d shards, total capacity: %zu entries\n", 
           num_shards, total_capacity);
}

/**
 * @brief 析构函数
 */
ShardedHashTable_512::~ShardedHashTable_512() {
    cleanup_gpu_memory();
    delete[] shards;
}

/**
 * @brief 初始化GPU内存
 */
bool ShardedHashTable_512::initialize_gpu_memory() {
    cudaError_t err;
    
    // 计算内存需求
    size_t shards_size = num_shards * sizeof(HashShard_512);
    size_t entries_size = total_capacity * sizeof(DP_Entry_512);
    size_t occupancy_size = total_capacity * sizeof(uint32_t);
    
    printf("[Sharded Hash] Allocating GPU memory: %.1f MB\n", 
           (shards_size + entries_size + occupancy_size) / (1024.0 * 1024.0));
    
    // 分配GPU内存
    err = cudaMalloc(&dev_shards, shards_size);
    if (err != cudaSuccess) {
        printf("[ERROR] Failed to allocate GPU shards memory: %s\n", cudaGetErrorString(err));
        return false;
    }
    
    err = cudaMalloc(&dev_entries_pool, entries_size);
    if (err != cudaSuccess) {
        printf("[ERROR] Failed to allocate GPU entries memory: %s\n", cudaGetErrorString(err));
        cudaFree(dev_shards);
        return false;
    }
    
    err = cudaMalloc(&dev_occupancy_pool, occupancy_size);
    if (err != cudaSuccess) {
        printf("[ERROR] Failed to allocate GPU occupancy memory: %s\n", cudaGetErrorString(err));
        cudaFree(dev_shards);
        cudaFree(dev_entries_pool);
        return false;
    }
    
    // 初始化分片指针
    for (int i = 0; i < num_shards; i++) {
        shards[i].entries = dev_entries_pool + i * ShardedHashConfig::ENTRIES_PER_SHARD;
        shards[i].occupancy = dev_occupancy_pool + i * ShardedHashConfig::ENTRIES_PER_SHARD;
    }
    
    // 复制分片信息到GPU
    err = cudaMemcpy(dev_shards, shards, shards_size, cudaMemcpyHostToDevice);
    if (err != cudaSuccess) {
        printf("[ERROR] Failed to copy shards to GPU: %s\n", cudaGetErrorString(err));
        cleanup_gpu_memory();
        return false;
    }
    
    // 初始化占用标记为0
    err = cudaMemset(dev_occupancy_pool, 0, occupancy_size);
    if (err != cudaSuccess) {
        printf("[ERROR] Failed to initialize occupancy: %s\n", cudaGetErrorString(err));
        cleanup_gpu_memory();
        return false;
    }
    
    printf("[Sharded Hash] GPU memory initialization successful\n");
    return true;
}

/**
 * @brief 释放GPU内存
 */
void ShardedHashTable_512::cleanup_gpu_memory() {
    if (dev_shards) {
        cudaFree(dev_shards);
        dev_shards = nullptr;
    }
    if (dev_entries_pool) {
        cudaFree(dev_entries_pool);
        dev_entries_pool = nullptr;
    }
    if (dev_occupancy_pool) {
        cudaFree(dev_occupancy_pool);
        dev_occupancy_pool = nullptr;
    }
}

/**
 * @brief 获取统计信息
 */
void ShardedHashTable_512::get_statistics(size_t &total_entries, double &load_factor, int &max_shard_load) const {
    total_entries = total_count;
    load_factor = (double)total_count / total_capacity;
    
    max_shard_load = 0;
    for (int i = 0; i < num_shards; i++) {
        if (shards[i].count > max_shard_load) {
            max_shard_load = shards[i].count;
        }
    }
}

/**
 * @brief 重置哈希表
 */
void ShardedHashTable_512::reset() {
    total_count = 0;
    for (int i = 0; i < num_shards; i++) {
        shards[i].count = 0;
    }
    
    if (dev_occupancy_pool) {
        size_t occupancy_size = total_capacity * sizeof(uint32_t);
        cudaMemset(dev_occupancy_pool, 0, occupancy_size);
    }
}

/**
 * @brief GPU内核：插入DP条目
 */
__global__ void insert_dp_entries_kernel(
    HashShard_512 *shards,
    const DP_Entry_512 *entries,
    int num_entries,
    int num_shards
) {
    int tid = blockIdx.x * blockDim.x + threadIdx.x;
    if (tid >= num_entries) return;
    
    const DP_Entry_512 &entry = entries[tid];
    if (!entry.is_valid()) return;
    
    // 计算分片索引
    uint64_t hash = entry.x.d[0] ^ entry.x.d[1] ^ entry.x.d[2] ^ entry.x.d[3];
    int shard_idx = hash % num_shards;
    
    HashShard_512 &shard = shards[shard_idx];
    
    // 计算哈希位置
    uint32_t pos = hash % shard.capacity;
    
    // 线性探测插入
    for (int probe = 0; probe < ShardedHashConfig::MAX_PROBE_DISTANCE; probe++) {
        uint32_t idx = (pos + probe) % shard.capacity;
        
        // 尝试原子性插入
        uint32_t old_occupancy = atomicCAS(&shard.occupancy[idx], 0, 1);
        if (old_occupancy == 0) {
            // 成功获得位置，插入条目
            shard.entries[idx] = entry;
            atomicAdd(&shard.count, 1);
            return;
        }
        
        // 检查是否已存在相同条目
        if (shard.entries[idx].x == entry.x) {
            // 已存在，不需要插入
            return;
        }
    }
    
    // 插入失败，分片已满
    // 在实际应用中可能需要扩容或使用溢出处理
}

/**
 * @brief GPU内核：查找DP碰撞
 */
__global__ void find_dp_collisions_kernel(
    HashShard_512 *shards,
    const DP_Entry_512 *query_entries,
    DP_Entry_512 *collision_results,
    int *collision_found,
    int num_queries,
    int num_shards
) {
    int tid = blockIdx.x * blockDim.x + threadIdx.x;
    if (tid >= num_queries) return;
    
    const DP_Entry_512 &query = query_entries[tid];
    if (!query.is_valid()) return;
    
    // 计算分片索引
    uint64_t hash = query.x.d[0] ^ query.x.d[1] ^ query.x.d[2] ^ query.x.d[3];
    int shard_idx = hash % num_shards;
    
    HashShard_512 &shard = shards[shard_idx];
    uint32_t pos = hash % shard.capacity;
    
    // 线性探测查找
    for (int probe = 0; probe < ShardedHashConfig::MAX_PROBE_DISTANCE; probe++) {
        uint32_t idx = (pos + probe) % shard.capacity;
        
        if (shard.occupancy[idx] == 0) {
            // 空位置，未找到
            break;
        }
        
        if (shard.entries[idx].x == query.x) {
            // 找到碰撞
            collision_results[tid] = shard.entries[idx];
            atomicAdd(collision_found, 1);
            return;
        }
    }
    
    // 未找到碰撞
    collision_results[tid] = DP_Entry_512(); // 空条目
}
