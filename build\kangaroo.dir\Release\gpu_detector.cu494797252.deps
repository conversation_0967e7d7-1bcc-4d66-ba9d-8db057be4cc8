C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\include\builtin_types.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\include\channel_descriptor.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\include\crt\common_functions.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\include\crt\cudacc_ext.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\include\crt\device_double_functions.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\include\crt\device_double_functions.hpp
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\include\crt\device_functions.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\include\crt\device_functions.hpp
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\include\crt\host_config.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\include\crt\host_defines.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\include\crt\math_functions.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\include\crt\math_functions.hpp
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\include\crt\sm_70_rt.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\include\crt\sm_80_rt.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\include\crt\sm_90_rt.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\include\cuda_device_runtime_api.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\include\cuda_runtime.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\include\cuda_runtime_api.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\include\device_atomic_functions.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\include\device_launch_parameters.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\include\device_types.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\include\driver_functions.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\include\driver_types.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\include\library_types.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\include\sm_20_atomic_functions.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\include\sm_20_intrinsics.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\include\sm_30_intrinsics.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\include\sm_32_atomic_functions.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\include\sm_32_intrinsics.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\include\sm_35_atomic_functions.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\include\sm_35_intrinsics.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\include\sm_60_atomic_functions.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\include\sm_61_intrinsics.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\include\surface_indirect_functions.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\include\surface_types.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\include\texture_indirect_functions.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\include\texture_types.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\include\vector_functions.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\include\vector_functions.hpp
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\include\vector_types.h
D:\mybitcoin\2\cuda-bsgs-production-full\Kangaroo\optimizations\phase1\gpu_detector.h
F:\visio\VC\Tools\MSVC\14.44.35207\include\algorithm
F:\visio\VC\Tools\MSVC\14.44.35207\include\ammintrin.h
F:\visio\VC\Tools\MSVC\14.44.35207\include\atomic
F:\visio\VC\Tools\MSVC\14.44.35207\include\cctype
F:\visio\VC\Tools\MSVC\14.44.35207\include\cerrno
F:\visio\VC\Tools\MSVC\14.44.35207\include\cfloat
F:\visio\VC\Tools\MSVC\14.44.35207\include\climits
F:\visio\VC\Tools\MSVC\14.44.35207\include\clocale
F:\visio\VC\Tools\MSVC\14.44.35207\include\cmath
F:\visio\VC\Tools\MSVC\14.44.35207\include\concurrencysal.h
F:\visio\VC\Tools\MSVC\14.44.35207\include\crtdefs.h
F:\visio\VC\Tools\MSVC\14.44.35207\include\cstddef
F:\visio\VC\Tools\MSVC\14.44.35207\include\cstdint
F:\visio\VC\Tools\MSVC\14.44.35207\include\cstdio
F:\visio\VC\Tools\MSVC\14.44.35207\include\cstdlib
F:\visio\VC\Tools\MSVC\14.44.35207\include\cstring
F:\visio\VC\Tools\MSVC\14.44.35207\include\ctime
F:\visio\VC\Tools\MSVC\14.44.35207\include\cwchar
F:\visio\VC\Tools\MSVC\14.44.35207\include\eh.h
F:\visio\VC\Tools\MSVC\14.44.35207\include\emmintrin.h
F:\visio\VC\Tools\MSVC\14.44.35207\include\exception
F:\visio\VC\Tools\MSVC\14.44.35207\include\immintrin.h
F:\visio\VC\Tools\MSVC\14.44.35207\include\initializer_list
F:\visio\VC\Tools\MSVC\14.44.35207\include\intrin.h
F:\visio\VC\Tools\MSVC\14.44.35207\include\intrin0.h
F:\visio\VC\Tools\MSVC\14.44.35207\include\intrin0.inl.h
F:\visio\VC\Tools\MSVC\14.44.35207\include\iomanip
F:\visio\VC\Tools\MSVC\14.44.35207\include\ios
F:\visio\VC\Tools\MSVC\14.44.35207\include\iosfwd
F:\visio\VC\Tools\MSVC\14.44.35207\include\iostream
F:\visio\VC\Tools\MSVC\14.44.35207\include\istream
F:\visio\VC\Tools\MSVC\14.44.35207\include\iterator
F:\visio\VC\Tools\MSVC\14.44.35207\include\limits
F:\visio\VC\Tools\MSVC\14.44.35207\include\limits.h
F:\visio\VC\Tools\MSVC\14.44.35207\include\memory
F:\visio\VC\Tools\MSVC\14.44.35207\include\mmintrin.h
F:\visio\VC\Tools\MSVC\14.44.35207\include\new
F:\visio\VC\Tools\MSVC\14.44.35207\include\nmmintrin.h
F:\visio\VC\Tools\MSVC\14.44.35207\include\ostream
F:\visio\VC\Tools\MSVC\14.44.35207\include\pmmintrin.h
F:\visio\VC\Tools\MSVC\14.44.35207\include\sal.h
F:\visio\VC\Tools\MSVC\14.44.35207\include\setjmp.h
F:\visio\VC\Tools\MSVC\14.44.35207\include\smmintrin.h
F:\visio\VC\Tools\MSVC\14.44.35207\include\stdexcept
F:\visio\VC\Tools\MSVC\14.44.35207\include\stdint.h
F:\visio\VC\Tools\MSVC\14.44.35207\include\streambuf
F:\visio\VC\Tools\MSVC\14.44.35207\include\string
F:\visio\VC\Tools\MSVC\14.44.35207\include\system_error
F:\visio\VC\Tools\MSVC\14.44.35207\include\tmmintrin.h
F:\visio\VC\Tools\MSVC\14.44.35207\include\typeinfo
F:\visio\VC\Tools\MSVC\14.44.35207\include\type_traits
F:\visio\VC\Tools\MSVC\14.44.35207\include\use_ansi.h
F:\visio\VC\Tools\MSVC\14.44.35207\include\utility
F:\visio\VC\Tools\MSVC\14.44.35207\include\vadefs.h
F:\visio\VC\Tools\MSVC\14.44.35207\include\vcruntime.h
F:\visio\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
F:\visio\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h
F:\visio\VC\Tools\MSVC\14.44.35207\include\vcruntime_new_debug.h
F:\visio\VC\Tools\MSVC\14.44.35207\include\vcruntime_string.h
F:\visio\VC\Tools\MSVC\14.44.35207\include\vcruntime_typeinfo.h
F:\visio\VC\Tools\MSVC\14.44.35207\include\vector
F:\visio\VC\Tools\MSVC\14.44.35207\include\wmmintrin.h
F:\visio\VC\Tools\MSVC\14.44.35207\include\xatomic.h
F:\visio\VC\Tools\MSVC\14.44.35207\include\xcall_once.h
F:\visio\VC\Tools\MSVC\14.44.35207\include\xerrc.h
F:\visio\VC\Tools\MSVC\14.44.35207\include\xfacet
F:\visio\VC\Tools\MSVC\14.44.35207\include\xiosbase
F:\visio\VC\Tools\MSVC\14.44.35207\include\xkeycheck.h
F:\visio\VC\Tools\MSVC\14.44.35207\include\xlocale
F:\visio\VC\Tools\MSVC\14.44.35207\include\xlocinfo
F:\visio\VC\Tools\MSVC\14.44.35207\include\xlocmon
F:\visio\VC\Tools\MSVC\14.44.35207\include\xlocnum
F:\visio\VC\Tools\MSVC\14.44.35207\include\xloctime
F:\visio\VC\Tools\MSVC\14.44.35207\include\xmemory
F:\visio\VC\Tools\MSVC\14.44.35207\include\xmmintrin.h
F:\visio\VC\Tools\MSVC\14.44.35207\include\xstring
F:\visio\VC\Tools\MSVC\14.44.35207\include\xthreads.h
F:\visio\VC\Tools\MSVC\14.44.35207\include\xtimec.h
F:\visio\VC\Tools\MSVC\14.44.35207\include\xtr1common
F:\visio\VC\Tools\MSVC\14.44.35207\include\xutility
F:\visio\VC\Tools\MSVC\14.44.35207\include\yvals.h
F:\visio\VC\Tools\MSVC\14.44.35207\include\yvals_core.h
F:\visio\VC\Tools\MSVC\14.44.35207\include\zmmintrin.h
F:\visio\VC\Tools\MSVC\14.44.35207\include\__msvc_bit_utils.hpp
F:\visio\VC\Tools\MSVC\14.44.35207\include\__msvc_heap_algorithms.hpp
F:\visio\VC\Tools\MSVC\14.44.35207\include\__msvc_iter_core.hpp
F:\visio\VC\Tools\MSVC\14.44.35207\include\__msvc_minmax.hpp
F:\visio\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp
F:\visio\VC\Tools\MSVC\14.44.35207\include\__msvc_sanitizer_annotate_container.hpp
F:\visio\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
F:\visio\VC\Tools\MSVC\14.44.35207\include\__msvc_system_error_abi.hpp
F:\visio\VC\Tools\MSVC\14.44.35207\include\__msvc_threads_core.hpp
F:\visio\VC\Tools\MSVC\14.44.35207\include\__msvc_xlocinfo_types.hpp
F:\Windows Kits\10\Include\10.0.26100.0\ucrt\corecrt.h
F:\Windows Kits\10\Include\10.0.26100.0\ucrt\corecrt_malloc.h
F:\Windows Kits\10\Include\10.0.26100.0\ucrt\corecrt_math.h
F:\Windows Kits\10\Include\10.0.26100.0\ucrt\corecrt_memcpy_s.h
F:\Windows Kits\10\Include\10.0.26100.0\ucrt\corecrt_memory.h
F:\Windows Kits\10\Include\10.0.26100.0\ucrt\corecrt_search.h
F:\Windows Kits\10\Include\10.0.26100.0\ucrt\corecrt_share.h
F:\Windows Kits\10\Include\10.0.26100.0\ucrt\corecrt_stdio_config.h
F:\Windows Kits\10\Include\10.0.26100.0\ucrt\corecrt_terminate.h
F:\Windows Kits\10\Include\10.0.26100.0\ucrt\corecrt_wconio.h
F:\Windows Kits\10\Include\10.0.26100.0\ucrt\corecrt_wctype.h
F:\Windows Kits\10\Include\10.0.26100.0\ucrt\corecrt_wdirect.h
F:\Windows Kits\10\Include\10.0.26100.0\ucrt\corecrt_wio.h
F:\Windows Kits\10\Include\10.0.26100.0\ucrt\corecrt_wprocess.h
F:\Windows Kits\10\Include\10.0.26100.0\ucrt\corecrt_wstdio.h
F:\Windows Kits\10\Include\10.0.26100.0\ucrt\corecrt_wstdlib.h
F:\Windows Kits\10\Include\10.0.26100.0\ucrt\corecrt_wstring.h
F:\Windows Kits\10\Include\10.0.26100.0\ucrt\corecrt_wtime.h
F:\Windows Kits\10\Include\10.0.26100.0\ucrt\crtdbg.h
F:\Windows Kits\10\Include\10.0.26100.0\ucrt\ctype.h
F:\Windows Kits\10\Include\10.0.26100.0\ucrt\errno.h
F:\Windows Kits\10\Include\10.0.26100.0\ucrt\float.h
F:\Windows Kits\10\Include\10.0.26100.0\ucrt\locale.h
F:\Windows Kits\10\Include\10.0.26100.0\ucrt\malloc.h
F:\Windows Kits\10\Include\10.0.26100.0\ucrt\math.h
F:\Windows Kits\10\Include\10.0.26100.0\ucrt\share.h
F:\Windows Kits\10\Include\10.0.26100.0\ucrt\stddef.h
F:\Windows Kits\10\Include\10.0.26100.0\ucrt\stdio.h
F:\Windows Kits\10\Include\10.0.26100.0\ucrt\stdlib.h
F:\Windows Kits\10\Include\10.0.26100.0\ucrt\string.h
F:\Windows Kits\10\Include\10.0.26100.0\ucrt\sys\stat.h
F:\Windows Kits\10\Include\10.0.26100.0\ucrt\sys\types.h
F:\Windows Kits\10\Include\10.0.26100.0\ucrt\time.h
F:\Windows Kits\10\Include\10.0.26100.0\ucrt\wchar.h
