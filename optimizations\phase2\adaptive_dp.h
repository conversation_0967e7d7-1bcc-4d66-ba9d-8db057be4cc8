#ifndef ADAPTIVE_DP_H
#define ADAPTIVE_DP_H

#include <cmath>
#include <algorithm>
#include <iostream>
#include <iomanip>
#include "SECPK1/Int.h"

/**
 * @file adaptive_dp.h
 * @brief 自适应Distinguished Point计算系统
 * 
 * 根据搜索范围、袋鼠数量和GPU架构自动计算最优DP位数
 * 支持动态调整以优化碰撞率和内存使用
 */

class AdaptiveDP {
private:
    uint32_t current_dp_bits;              // 当前DP位数
    uint64_t total_kangaroos;              // 总袋鼠数量
    uint64_t range_size_bits;              // 搜索范围位数
    double collision_rate;                 // 当前碰撞率
    uint64_t total_operations;             // 总操作数
    uint64_t total_collisions;             // 总碰撞数
    
    // GPU架构信息
    int gpu_major;                         // GPU计算能力主版本
    int gpu_minor;                         // GPU计算能力次版本
    size_t gpu_memory;                     // GPU内存大小
    
    // 统计信息
    uint64_t dp_found_count;               // 找到的DP数量
    uint64_t last_adjustment_ops;          // 上次调整时的操作数
    double target_collision_rate;          // 目标碰撞率
    
public:
    /**
     * @brief 构造函数
     */
    AdaptiveDP();
    
    /**
     * @brief 析构函数
     */
    ~AdaptiveDP();
    
    /**
     * @brief 初始化GPU信息
     * @param device_id GPU设备ID
     */
    void initializeGPUInfo(int device_id);
    
    /**
     * @brief 计算最优DP位数
     * @param range_start 搜索范围起始值
     * @param range_end 搜索范围结束值
     * @param num_kangaroos 袋鼠数量
     * @return 最优DP位数
     */
    uint32_t calculateOptimalDP(const Int& range_start, 
                               const Int& range_end,
                               uint64_t num_kangaroos);
    
    /**
     * @brief 动态调整DP位数
     * @param current_collision_rate 当前碰撞率
     * @param operations 当前操作数
     * @return 是否进行了调整
     */
    bool adjustDP(double current_collision_rate, uint64_t operations);
    
    /**
     * @brief 获取当前DP掩码
     * @return DP掩码值
     */
    uint64_t getDPMask() const;
    
    /**
     * @brief 获取当前DP位数
     * @return DP位数
     */
    uint32_t getDPBits() const { return current_dp_bits; }
    
    /**
     * @brief 更新统计信息
     * @param new_operations 新增操作数
     * @param new_collisions 新增碰撞数
     * @param new_dp_found 新增DP数量
     */
    void updateStatistics(uint64_t new_operations, 
                         uint32_t new_collisions,
                         uint32_t new_dp_found);
    
    /**
     * @brief 检查是否需要调整DP
     * @return 是否需要调整
     */
    bool needsAdjustment() const;
    
    /**
     * @brief 计算理论期望步数
     * @param range_bits 搜索范围位数
     * @return 理论期望步数
     */
    static double calculateExpectedSteps(uint64_t range_bits);
    
    /**
     * @brief 计算内存使用量
     * @param dp_bits DP位数
     * @param kangaroo_count 袋鼠数量
     * @return 预估内存使用量(字节)
     */
    static size_t calculateMemoryUsage(uint32_t dp_bits, uint64_t kangaroo_count);
    
    /**
     * @brief 获取GPU架构推荐的DP调整
     * @param major GPU计算能力主版本
     * @param minor GPU计算能力次版本
     * @return DP位数调整值
     */
    static int getGPUArchitectureAdjustment(int major, int minor);
    
    /**
     * @brief 打印DP信息
     */
    void printDPInfo() const;
    
    /**
     * @brief 打印详细统计信息
     */
    void printDetailedStatistics() const;
    
    /**
     * @brief 重置统计信息
     */
    void resetStatistics();
    
    /**
     * @brief 设置目标碰撞率
     * @param target_rate 目标碰撞率 (0.01-0.1)
     */
    void setTargetCollisionRate(double target_rate);
    
    /**
     * @brief 获取性能指标
     * @return 性能指标结构
     */
    struct PerformanceMetrics {
        double collision_rate;              // 碰撞率
        double dp_density;                  // DP密度
        double memory_efficiency;          // 内存效率
        double theoretical_progress;        // 理论进度
        uint64_t operations_per_second;     // 每秒操作数
    };
    
    PerformanceMetrics getPerformanceMetrics() const;
    
private:
    /**
     * @brief 计算基础DP位数
     * @param range_bits 搜索范围位数
     * @param kangaroo_count 袋鼠数量
     * @return 基础DP位数
     */
    uint32_t calculateBaseDPBits(uint64_t range_bits, uint64_t kangaroo_count) const;
    
    /**
     * @brief 应用GPU架构调整
     * @param base_dp_bits 基础DP位数
     * @return 调整后的DP位数
     */
    uint32_t applyGPUAdjustment(uint32_t base_dp_bits) const;
    
    /**
     * @brief 应用内存限制
     * @param dp_bits DP位数
     * @param kangaroo_count 袋鼠数量
     * @return 调整后的DP位数
     */
    uint32_t applyMemoryConstraints(uint32_t dp_bits, uint64_t kangaroo_count) const;
    
    /**
     * @brief 验证DP位数合理性
     * @param dp_bits DP位数
     * @return 验证后的DP位数
     */
    uint32_t validateDPBits(uint32_t dp_bits) const;
};

/**
 * @brief DP计算工具函数命名空间
 */
namespace DPUtils {
    /**
     * @brief 计算DP密度
     * @param dp_bits DP位数
     * @return DP密度 (0-1)
     */
    double calculateDPDensity(uint32_t dp_bits);
    
    /**
     * @brief 估算哈希表大小
     * @param dp_bits DP位数
     * @param expected_operations 预期操作数
     * @return 预估哈希表大小
     */
    size_t estimateHashTableSize(uint32_t dp_bits, uint64_t expected_operations);
    
    /**
     * @brief 计算碰撞概率
     * @param dp_bits DP位数
     * @param operations 操作数
     * @return 碰撞概率
     */
    double calculateCollisionProbability(uint32_t dp_bits, uint64_t operations);
}

#endif // ADAPTIVE_DP_H
