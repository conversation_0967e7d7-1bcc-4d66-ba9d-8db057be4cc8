#ifndef GPU_DETECTOR_H
#define GPU_DETECTOR_H

#include <cuda_runtime.h>
#include <vector>
#include <string>

/**
 * @file gpu_detector.h
 * @brief GPU自动检测和架构适配系统
 * 
 * 支持NVIDIA全系列GPU (SM 5.2 - SM 9.0)
 * 自动检测GPU能力并提供最优配置建议
 */

struct GPUInfo {
    int device_id;                          // GPU设备ID
    std::string name;                       // GPU名称
    int major;                              // 计算能力主版本
    int minor;                              // 计算能力次版本
    size_t total_memory;                    // 总显存 (字节)
    size_t free_memory;                     // 可用显存 (字节)
    int multiprocessor_count;               // SM数量
    int max_threads_per_block;              // 每块最大线程数
    int max_threads_per_multiprocessor;     // 每SM最大线程数
    int warp_size;                          // Warp大小
    size_t shared_memory_per_block;         // 每块共享内存
    size_t shared_memory_per_multiprocessor; // 每SM共享内存
    int max_blocks_per_multiprocessor;      // 每SM最大块数
    
    // 高级特性支持
    bool supports_cooperative_groups;       // 协作组支持
    bool supports_unified_memory;           // 统一内存支持
    bool supports_cuda_graphs;              // CUDA图支持
    bool supports_async_memory;             // 异步内存支持
    
    // 性能特征
    int memory_clock_rate;                  // 内存时钟频率
    int memory_bus_width;                   // 内存总线宽度
    double memory_bandwidth;                // 内存带宽 (GB/s)
    int core_clock_rate;                    // 核心时钟频率
};

class GPUDetector {
public:
    /**
     * @brief 检测所有可用的CUDA GPU
     * @return GPU信息列表
     */
    static std::vector<GPUInfo> detectAllGPUs();
    
    /**
     * @brief 获取最佳GPU (基于性能评分)
     * @return 最佳GPU信息
     */
    static GPUInfo getBestGPU();
    
    /**
     * @brief 检查GPU架构是否支持
     * @param major 计算能力主版本
     * @param minor 计算能力次版本
     * @return 是否支持
     */
    static bool isArchitectureSupported(int major, int minor);
    
    /**
     * @brief 获取GPU架构名称
     * @param major 计算能力主版本
     * @param minor 计算能力次版本
     * @return 架构名称
     */
    static std::string getArchitectureName(int major, int minor);
    
    /**
     * @brief 计算GPU性能评分
     * @param info GPU信息
     * @return 性能评分 (越高越好)
     */
    static double calculatePerformanceScore(const GPUInfo& info);
    
    /**
     * @brief 获取推荐的Kangaroo配置
     * @param info GPU信息
     * @return 推荐配置参数
     */
    static struct KangarooGPUConfig getRecommendedConfig(const GPUInfo& info);
    
    /**
     * @brief 打印GPU详细信息
     * @param info GPU信息
     */
    static void printGPUInfo(const GPUInfo& info);
    
    /**
     * @brief 打印所有GPU信息
     */
    static void printAllGPUs();
    
    /**
     * @brief 检查CUDA运行时环境
     * @return 是否正常
     */
    static bool checkCUDAEnvironment();
    
private:
    static void fillAdvancedInfo(GPUInfo& info, int device_id);
    static double calculateMemoryBandwidth(const GPUInfo& info);
};

/**
 * @brief Kangaroo算法GPU配置
 */
struct KangarooGPUConfig {
    uint32_t blocks_per_sm;                 // 每SM块数
    uint32_t threads_per_block;             // 每块线程数
    uint32_t kangaroos_per_sm;              // 每SM袋鼠数
    uint32_t shared_memory_usage;           // 共享内存使用量
    uint32_t optimal_dp_bits;               // 推荐DP位数
    bool use_cooperative_groups;            // 是否使用协作组
    bool use_async_memory;                  // 是否使用异步内存
    double memory_usage_ratio;              // 内存使用比例
};

#endif // GPU_DETECTOR_H
