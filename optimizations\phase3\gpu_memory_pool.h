#ifndef GPU_MEMORY_POOL_H
#define GPU_MEMORY_POOL_H

#include <cuda_runtime.h>
#include <vector>
#include <memory>
#include <mutex>
#include <atomic>
#include <unordered_map>

/**
 * @file gpu_memory_pool.h
 * @brief GPU内存池管理系统
 * 
 * 高效的GPU内存分配和管理，减少cudaMalloc/cudaFree开销
 * 支持异步分配、内存复用和碎片整理
 */

/**
 * @brief 内存块结构
 */
struct MemoryBlock {
    void* ptr;                      // 内存指针
    size_t size;                    // 块大小
    bool in_use;                    // 是否使用中
    cudaStream_t stream;            // 关联的CUDA流
    int device_id;                  // GPU设备ID
    uint64_t allocation_id;         // 分配ID
    std::chrono::steady_clock::time_point last_used;  // 最后使用时间
    
    MemoryBlock() : ptr(nullptr), size(0), in_use(false), stream(0), 
                   device_id(0), allocation_id(0) {}
};

/**
 * @brief 内存池统计信息
 */
struct MemoryPoolStatistics {
    size_t total_allocated;         // 总分配内存
    size_t total_used;              // 当前使用内存
    size_t peak_usage;              // 峰值使用量
    uint64_t allocation_count;      // 分配次数
    uint64_t deallocation_count;    // 释放次数
    uint64_t reuse_count;           // 复用次数
    double fragmentation_ratio;     // 碎片率
    double reuse_ratio;             // 复用率
    
    MemoryPoolStatistics() : total_allocated(0), total_used(0), peak_usage(0),
                            allocation_count(0), deallocation_count(0), reuse_count(0),
                            fragmentation_ratio(0.0), reuse_ratio(0.0) {}
};

/**
 * @brief GPU内存池类
 */
class GPUMemoryPool {
private:
    std::vector<std::unique_ptr<MemoryBlock>> blocks;
    std::mutex pool_mutex;
    int device_id;
    
    // 统计信息
    std::atomic<size_t> total_allocated;
    std::atomic<size_t> total_used;
    std::atomic<size_t> peak_usage;
    std::atomic<uint64_t> allocation_count;
    std::atomic<uint64_t> deallocation_count;
    std::atomic<uint64_t> reuse_count;
    std::atomic<uint64_t> next_allocation_id;
    
    // 配置参数
    size_t max_pool_size;           // 最大池大小
    size_t min_block_size;          // 最小块大小
    size_t max_block_size;          // 最大块大小
    double fragmentation_threshold; // 碎片整理阈值
    bool auto_cleanup_enabled;      // 自动清理
    
    // 快速查找表
    std::unordered_map<void*, size_t> ptr_to_block_index;
    
public:
    /**
     * @brief 构造函数
     * @param device_id GPU设备ID
     * @param max_size_gb 最大池大小(GB)
     */
    GPUMemoryPool(int device_id, size_t max_size_gb = 16);
    
    /**
     * @brief 析构函数
     */
    ~GPUMemoryPool();
    
    /**
     * @brief 分配内存
     * @param size 内存大小
     * @param stream CUDA流 (可选)
     * @return 内存指针，失败返回nullptr
     */
    void* allocate(size_t size, cudaStream_t stream = 0);
    
    /**
     * @brief 释放内存
     * @param ptr 内存指针
     */
    void deallocate(void* ptr);
    
    /**
     * @brief 异步分配内存
     * @param size 内存大小
     * @param stream CUDA流
     * @return 内存指针，失败返回nullptr
     */
    void* allocateAsync(size_t size, cudaStream_t stream);
    
    /**
     * @brief 异步释放内存
     * @param ptr 内存指针
     * @param stream CUDA流
     */
    void deallocateAsync(void* ptr, cudaStream_t stream);
    
    /**
     * @brief 预分配内存块
     * @param sizes 内存大小列表
     * @return 是否成功
     */
    bool preallocate(const std::vector<size_t>& sizes);
    
    /**
     * @brief 清理未使用的内存块
     * @param force 是否强制清理
     */
    void cleanup(bool force = false);
    
    /**
     * @brief 内存碎片整理
     */
    void defragment();
    
    /**
     * @brief 获取统计信息
     * @return 统计信息结构
     */
    MemoryPoolStatistics getStatistics() const;
    
    /**
     * @brief 打印统计信息
     */
    void printStatistics() const;
    
    /**
     * @brief 打印详细的内存块信息
     */
    void printDetailedBlockInfo() const;
    
    /**
     * @brief 获取总分配内存
     */
    size_t getTotalAllocated() const { return total_allocated.load(); }
    
    /**
     * @brief 获取当前使用内存
     */
    size_t getTotalUsed() const { return total_used.load(); }
    
    /**
     * @brief 获取峰值使用量
     */
    size_t getPeakUsage() const { return peak_usage.load(); }
    
    /**
     * @brief 获取可用内存
     */
    size_t getAvailableMemory() const;
    
    /**
     * @brief 设置自动清理
     * @param enabled 是否启用
     * @param threshold_mb 清理阈值(MB)
     */
    void setAutoCleanup(bool enabled, size_t threshold_mb = 1024);
    
    /**
     * @brief 设置碎片整理阈值
     * @param threshold 碎片率阈值 (0.0-1.0)
     */
    void setFragmentationThreshold(double threshold);
    
    /**
     * @brief 检查内存池健康状态
     * @return 健康状态描述
     */
    struct HealthStatus {
        bool is_healthy;            // 是否健康
        double fragmentation;       // 碎片率
        double utilization;         // 利用率
        std::string status_message; // 状态消息
    };
    
    HealthStatus checkHealth() const;
    
    /**
     * @brief 优化内存池配置
     */
    void optimize();
    
    /**
     * @brief 重置内存池
     */
    void reset();
    
    /**
     * @brief 导出内存使用报告
     * @param filename 文件名
     * @return 是否成功
     */
    bool exportUsageReport(const std::string& filename) const;
    
private:
    /**
     * @brief 查找合适的空闲块
     * @param size 所需大小
     * @return 块索引，未找到返回-1
     */
    int findFreeBlock(size_t size);
    
    /**
     * @brief 创建新的内存块
     * @param size 块大小
     * @param stream CUDA流
     * @return 块索引，失败返回-1
     */
    int createNewBlock(size_t size, cudaStream_t stream);
    
    /**
     * @brief 分割内存块
     * @param block_index 块索引
     * @param size 所需大小
     * @return 是否成功
     */
    bool splitBlock(int block_index, size_t size);
    
    /**
     * @brief 合并相邻的空闲块
     */
    void mergeAdjacentBlocks();
    
    /**
     * @brief 更新统计信息
     */
    void updateStatistics();
    
    /**
     * @brief 计算碎片率
     * @return 碎片率 (0.0-1.0)
     */
    double calculateFragmentation() const;
    
    /**
     * @brief 对齐内存大小
     * @param size 原始大小
     * @return 对齐后的大小
     */
    size_t alignSize(size_t size) const;
    
    /**
     * @brief 检查是否需要清理
     * @return 是否需要清理
     */
    bool needsCleanup() const;
    
    /**
     * @brief 清理过期的内存块
     */
    void cleanupExpiredBlocks();
};

/**
 * @brief GPU内存池管理器 (单例模式)
 */
class GPUMemoryPoolManager {
private:
    static std::unique_ptr<GPUMemoryPoolManager> instance;
    static std::mutex instance_mutex;
    
    std::unordered_map<int, std::unique_ptr<GPUMemoryPool>> pools;
    std::mutex pools_mutex;
    
    GPUMemoryPoolManager() = default;
    
public:
    /**
     * @brief 获取单例实例
     */
    static GPUMemoryPoolManager& getInstance();
    
    /**
     * @brief 获取指定设备的内存池
     * @param device_id GPU设备ID
     * @return 内存池指针
     */
    GPUMemoryPool* getPool(int device_id);
    
    /**
     * @brief 创建内存池
     * @param device_id GPU设备ID
     * @param max_size_gb 最大大小(GB)
     * @return 是否成功
     */
    bool createPool(int device_id, size_t max_size_gb = 16);
    
    /**
     * @brief 销毁内存池
     * @param device_id GPU设备ID
     */
    void destroyPool(int device_id);
    
    /**
     * @brief 销毁所有内存池
     */
    void destroyAllPools();
    
    /**
     * @brief 打印所有内存池统计信息
     */
    void printAllPoolsStatistics() const;
    
    /**
     * @brief 优化所有内存池
     */
    void optimizeAllPools();
    
    /**
     * @brief 获取总内存使用量
     * @return 总内存使用量(字节)
     */
    size_t getTotalMemoryUsage() const;
};

/**
 * @brief RAII GPU内存管理器
 */
template<typename T>
class GPUMemoryRAII {
private:
    T* ptr;
    size_t size;
    int device_id;
    GPUMemoryPool* pool;
    
public:
    GPUMemoryRAII(size_t count, int device_id = 0) 
        : ptr(nullptr), size(count * sizeof(T)), device_id(device_id) {
        pool = GPUMemoryPoolManager::getInstance().getPool(device_id);
        if (pool) {
            ptr = static_cast<T*>(pool->allocate(size));
        }
    }
    
    ~GPUMemoryRAII() {
        if (ptr && pool) {
            pool->deallocate(ptr);
        }
    }
    
    T* get() const { return ptr; }
    T* operator->() const { return ptr; }
    T& operator*() const { return *ptr; }
    
    bool isValid() const { return ptr != nullptr; }
    size_t getSize() const { return size; }
    
    // 禁止拷贝
    GPUMemoryRAII(const GPUMemoryRAII&) = delete;
    GPUMemoryRAII& operator=(const GPUMemoryRAII&) = delete;
    
    // 允许移动
    GPUMemoryRAII(GPUMemoryRAII&& other) noexcept 
        : ptr(other.ptr), size(other.size), device_id(other.device_id), pool(other.pool) {
        other.ptr = nullptr;
        other.pool = nullptr;
    }
    
    GPUMemoryRAII& operator=(GPUMemoryRAII&& other) noexcept {
        if (this != &other) {
            if (ptr && pool) {
                pool->deallocate(ptr);
            }
            ptr = other.ptr;
            size = other.size;
            device_id = other.device_id;
            pool = other.pool;
            other.ptr = nullptr;
            other.pool = nullptr;
        }
        return *this;
    }
};

#endif // GPU_MEMORY_POOL_H
