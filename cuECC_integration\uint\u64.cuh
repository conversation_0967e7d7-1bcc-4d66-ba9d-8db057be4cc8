#ifndef _U64_CUH
#define _U64_CUH

// 🔧 CUDA兼容性宏定义
#ifndef __CUDACC__
#define __forceinline__ inline
#define __device__
#define __host__
// 🔧 修复：为C++编译提供__umul64hi的实现
#ifdef _MSC_VER
#include <intrin.h>
#define __umul64hi(a, b) __umulh(a, b)
#else
// GCC/Clang实现
static inline unsigned long long __umul64hi(unsigned long long a, unsigned long long b) {
    return ((unsigned __int128)a * b) >> 64;
}
#endif
#endif

typedef unsigned long long u64;

__forceinline__ __device__ bool u64Add(u64 *output, const u64 a, const u64 b,
                                       const u64 carry) {
  *output = a + b + carry;
  return (*output < a) || (*output < b);
}

__forceinline__ __device__ bool u64Sub(u64 *output, const u64 a, const u64 b,
                                       const u64 borrow) {
  *output = a - b - borrow;
  return a < b;
}

__forceinline__ __device__ u64 u64Mul(u64 *output, const u64 a, const u64 b,
                                      const u64 carry) {
  u64 low = a * b;
  u64 high = __umul64hi(a, b);
  u64 oldLow = low;
  low += carry;

  if (low < oldLow) {
    high++;
  }

  *output = low;

  return high;
}

#endif
