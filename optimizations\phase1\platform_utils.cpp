#include "platform_utils.h"
#include <iostream>
#include <sstream>
#include <algorithm>

#ifdef PLATFORM_WINDOWS
#include <sys/stat.h>
#else
#include <sys/stat.h>
#include <sys/sysinfo.h>
#include <sys/mman.h>
#include <errno.h>
#endif

bool PlatformUtils::platform_initialized = false;

#ifdef PLATFORM_WINDOWS
LARGE_INTEGER PlatformUtils::frequency;
bool PlatformUtils::frequency_initialized = false;
#endif

void PlatformUtils::initializePlatform() {
    if (platform_initialized) return;

#ifdef PLATFORM_WINDOWS
    // 初始化高精度计时器
    if (!frequency_initialized) {
        QueryPerformanceFrequency(&frequency);
        frequency_initialized = true;
    }

    // 设置控制台UTF-8编码
    SetConsoleOutputCP(CP_UTF8);
#endif

    platform_initialized = true;
}

void PlatformUtils::cleanupPlatform() {
    if (!platform_initialized) return;

    platform_initialized = false;
}

uint64_t PlatformUtils::getCurrentTimeMs() {
#ifdef PLATFORM_WINDOWS
    if (!frequency_initialized) {
        QueryPerformanceFrequency(&frequency);
        frequency_initialized = true;
    }
    
    LARGE_INTEGER counter;
    QueryPerformanceCounter(&counter);
    return (counter.QuadPart * 1000) / frequency.QuadPart;
#else
    struct timeval tv;
    gettimeofday(&tv, nullptr);
    return (uint64_t)tv.tv_sec * 1000 + tv.tv_usec / 1000;
#endif
}

uint64_t PlatformUtils::getCurrentTimeUs() {
#ifdef PLATFORM_WINDOWS
    if (!frequency_initialized) {
        QueryPerformanceFrequency(&frequency);
        frequency_initialized = true;
    }
    
    LARGE_INTEGER counter;
    QueryPerformanceCounter(&counter);
    return (counter.QuadPart * 1000000) / frequency.QuadPart;
#else
    struct timeval tv;
    gettimeofday(&tv, nullptr);
    return (uint64_t)tv.tv_sec * 1000000 + tv.tv_usec;
#endif
}

void PlatformUtils::sleepMs(uint32_t milliseconds) {
#ifdef PLATFORM_WINDOWS
    Sleep(milliseconds);
#else
    usleep(milliseconds * 1000);
#endif
}

void PlatformUtils::sleepUs(uint32_t microseconds) {
#ifdef PLATFORM_WINDOWS
    // Windows没有微秒级sleep，使用高精度等待
    uint64_t start = getCurrentTimeUs();
    while (getCurrentTimeUs() - start < microseconds) {
        Sleep(0);  // 让出CPU时间片
    }
#else
    usleep(microseconds);
#endif
}

int PlatformUtils::getCPUCoreCount() {
#ifdef PLATFORM_WINDOWS
    SYSTEM_INFO sysinfo;
    GetSystemInfo(&sysinfo);
    return sysinfo.dwNumberOfProcessors;
#else
    return sysconf(_SC_NPROCESSORS_ONLN);
#endif
}

size_t PlatformUtils::getSystemMemory() {
#ifdef PLATFORM_WINDOWS
    MEMORYSTATUSEX memInfo;
    memInfo.dwLength = sizeof(MEMORYSTATUSEX);
    GlobalMemoryStatusEx(&memInfo);
    return memInfo.ullTotalPhys;
#else
    struct sysinfo info;
    if (sysinfo(&info) == 0) {
        return info.totalram * info.mem_unit;
    }
    return 0;
#endif
}

size_t PlatformUtils::getAvailableMemory() {
#ifdef PLATFORM_WINDOWS
    MEMORYSTATUSEX memInfo;
    memInfo.dwLength = sizeof(MEMORYSTATUSEX);
    GlobalMemoryStatusEx(&memInfo);
    return memInfo.ullAvailPhys;
#else
    struct sysinfo info;
    if (sysinfo(&info) == 0) {
        return info.freeram * info.mem_unit;
    }
    return 0;
#endif
}

std::string PlatformUtils::getPlatformName() {
#ifdef PLATFORM_WINDOWS
    return "Windows";
#else
    return "Linux";
#endif
}

std::string PlatformUtils::getSystemArchitecture() {
#ifdef PLATFORM_WINDOWS
    SYSTEM_INFO sysinfo;
    GetSystemInfo(&sysinfo);
    switch (sysinfo.wProcessorArchitecture) {
        case PROCESSOR_ARCHITECTURE_AMD64:
            return "x64";
        case PROCESSOR_ARCHITECTURE_ARM64:
            return "ARM64";
        case PROCESSOR_ARCHITECTURE_INTEL:
            return "x86";
        default:
            return "Unknown";
    }
#else
    #ifdef __x86_64__
        return "x64";
    #elif __aarch64__
        return "ARM64";
    #elif __i386__
        return "x86";
    #else
        return "Unknown";
    #endif
#endif
}

void PlatformUtils::setThreadPriority(int priority) {
#ifdef PLATFORM_WINDOWS
    HANDLE thread = GetCurrentThread();
    int win_priority = THREAD_PRIORITY_NORMAL;
    
    if (priority > 10) win_priority = THREAD_PRIORITY_HIGHEST;
    else if (priority > 5) win_priority = THREAD_PRIORITY_ABOVE_NORMAL;
    else if (priority < -10) win_priority = THREAD_PRIORITY_LOWEST;
    else if (priority < -5) win_priority = THREAD_PRIORITY_BELOW_NORMAL;
    
    SetThreadPriority(thread, win_priority);
#else
    // Linux使用nice值
    pthread_t thread = pthread_self();
    struct sched_param param;
    int policy;
    
    pthread_getschedparam(thread, &policy, &param);
    param.sched_priority = std::max(-20, std::min(20, priority));
    pthread_setschedparam(thread, policy, &param);
#endif
}

size_t PlatformUtils::getPageSize() {
#ifdef PLATFORM_WINDOWS
    SYSTEM_INFO sysinfo;
    GetSystemInfo(&sysinfo);
    return sysinfo.dwPageSize;
#else
    return sysconf(_SC_PAGESIZE);
#endif
}

bool PlatformUtils::lockMemory(void* addr, size_t size) {
#ifdef PLATFORM_WINDOWS
    return VirtualLock(addr, size) != 0;
#else
    return mlock(addr, size) == 0;
#endif
}

bool PlatformUtils::unlockMemory(void* addr, size_t size) {
#ifdef PLATFORM_WINDOWS
    return VirtualUnlock(addr, size) != 0;
#else
    return munlock(addr, size) == 0;
#endif
}

uint32_t PlatformUtils::hostToNetwork32(uint32_t host_int) {
    // 简单的字节序转换
    return ((host_int & 0xFF) << 24) |
           ((host_int & 0xFF00) << 8) |
           ((host_int & 0xFF0000) >> 8) |
           ((host_int & 0xFF000000) >> 24);
}

uint32_t PlatformUtils::networkToHost32(uint32_t net_int) {
    return hostToNetwork32(net_int);  // 字节序转换是对称的
}

bool PlatformUtils::createDirectory(const std::string& path) {
#ifdef PLATFORM_WINDOWS
    return CreateDirectoryA(path.c_str(), nullptr) != 0 || GetLastError() == ERROR_ALREADY_EXISTS;
#else
    return mkdir(path.c_str(), 0755) == 0 || errno == EEXIST;
#endif
}

bool PlatformUtils::fileExists(const std::string& path) {
#ifdef PLATFORM_WINDOWS
    DWORD attrib = GetFileAttributesA(path.c_str());
    return attrib != INVALID_FILE_ATTRIBUTES;
#else
    return access(path.c_str(), F_OK) == 0;
#endif
}

int64_t PlatformUtils::getFileSize(const std::string& path) {
#ifdef PLATFORM_WINDOWS
    HANDLE file = CreateFileA(path.c_str(), GENERIC_READ, FILE_SHARE_READ, 
                             nullptr, OPEN_EXISTING, FILE_ATTRIBUTE_NORMAL, nullptr);
    if (file == INVALID_HANDLE_VALUE) return -1;
    
    LARGE_INTEGER size;
    if (!GetFileSizeEx(file, &size)) {
        CloseHandle(file);
        return -1;
    }
    
    CloseHandle(file);
    return size.QuadPart;
#else
    struct stat st;
    if (stat(path.c_str(), &st) == 0) {
        return st.st_size;
    }
    return -1;
#endif
}

std::string PlatformUtils::getEnvironmentVariable(const std::string& name, 
                                                 const std::string& default_value) {
#ifdef PLATFORM_WINDOWS
    char buffer[32768];
    DWORD result = GetEnvironmentVariableA(name.c_str(), buffer, sizeof(buffer));
    if (result > 0 && result < sizeof(buffer)) {
        return std::string(buffer);
    }
    return default_value;
#else
    const char* value = getenv(name.c_str());
    return value ? std::string(value) : default_value;
#endif
}

bool PlatformUtils::setEnvironmentVariable(const std::string& name, const std::string& value) {
#ifdef PLATFORM_WINDOWS
    return SetEnvironmentVariableA(name.c_str(), value.c_str()) != 0;
#else
    return setenv(name.c_str(), value.c_str(), 1) == 0;
#endif
}
