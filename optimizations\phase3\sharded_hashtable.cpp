#include "sharded_hashtable.h"
#include <iostream>
#include <iomanip>
#include <algorithm>
#include <cmath>
#include <fstream>
#include <chrono>

// 避免Windows.h的min/max宏冲突
#ifdef max
#undef max
#endif
#ifdef min
#undef min
#endif

// 简化实现，避免复杂的Int类操作

ShardedHashTable::ShardedHashTable(size_t target_memory_gb, uint32_t num_shards_hint) 
    : target_memory_gb(target_memory_gb), max_load_factor(0.75), auto_resize_enabled(true),
      total_items(0), total_memory_used(0), insert_operations(0), 
      lookup_operations(0), collision_found_count(0) {
    
    // 计算分片数量
    if (num_shards_hint > 0) {
        num_shards = std::min(num_shards_hint, ShardConfig::MAX_SHARDS);
    } else {
        num_shards = std::min((uint32_t)((target_memory_gb + 3) / 4), ShardConfig::MAX_SHARDS);
        num_shards = std::max(num_shards, 4u);  // 最少4个分片
    }
    
    initializeShards();
    
    std::cout << "ShardedHashTable: Initialized " << num_shards 
              << " shards, target memory: " << target_memory_gb << " GB" << std::endl;
}

ShardedHashTable::~ShardedHashTable() {
    clear();
}

void ShardedHashTable::initializeShards() {
    shards.resize(num_shards);
    shard_mutexes.resize(num_shards);
    shard_sizes.resize(num_shards);
    shard_collisions.resize(num_shards);
    
    // 初始化原子变量
    for (uint32_t i = 0; i < num_shards; i++) {
        shard_sizes[i].store(0);
        shard_collisions[i].store(0);
        
        // 预分配内存以提高性能
        shards[i].reserve((ShardConfig::SHARD_SIZE / sizeof(uint64_t)) / 8);  // 预留12.5%负载因子
    }
}

uint32_t ShardedHashTable::getShardIndex(const uint128_t& key) const {
    // 使用高质量哈希函数确保均匀分布
    uint64_t hash = highQualityHash(key);
    return hash % num_shards;
}

uint64_t ShardedHashTable::highQualityHash(const uint128_t& key) const {
    // 使用FNV-1a哈希算法的变体
    const uint64_t FNV_OFFSET_BASIS = 14695981039346656037ULL;
    const uint64_t FNV_PRIME = 1099511628211ULL;
    
    uint64_t hash = FNV_OFFSET_BASIS;
    
    // 处理低64位
    hash ^= key.low;
    hash *= FNV_PRIME;
    
    // 处理高64位
    hash ^= key.high;
    hash *= FNV_PRIME;
    
    // 额外的混合步骤
    hash ^= hash >> 32;
    hash *= FNV_PRIME;
    hash ^= hash >> 16;
    
    return hash;
}

uint128_t ShardedHashTable::makeKey(uint64_t value) const {
    uint128_t key;

    // 简化实现：从64位值创建128位键
    key.low = value;
    key.high = value ^ 0xAAAAAAAAAAAAAAAAULL;  // 简单的扩展

    return key;
}

bool ShardedHashTable::insert(const uint128_t& key, uint64_t data) {
    uint32_t shard_idx = getShardIndex(key);
    
    std::lock_guard<std::mutex> lock(shard_mutexes[shard_idx]);
    auto& shard = shards[shard_idx];
    
    insert_operations.fetch_add(1);
    
    // 检查是否已存在
    auto it = shard.find(key);
    if (it != shard.end()) {
        // 发现碰撞!
        if (checkCollision(key, key)) {  // 简化的碰撞检查
            collision_found_count.fetch_add(1);

            std::cout << "\n🎉 COLLISION FOUND! 🎉" << std::endl;
            std::cout << "Key collision detected!" << std::endl;

            return true;  // 找到解
        }
        shard_collisions[shard_idx].fetch_add(1);
        return false;  // 假碰撞
    }

    // 插入新项
    shard[key] = data;
    shard_sizes[shard_idx].fetch_add(1);
    total_items.fetch_add(1);
    
    // 检查是否需要调整大小
    if (auto_resize_enabled && needsResize()) {
        // 在后台线程中执行调整大小 (这里简化为直接执行)
        // 实际实现中应该使用异步调整
    }
    
    return false;
}

bool ShardedHashTable::insertData(uint64_t data) {
    uint128_t key = makeKey(data);
    return insert(key, data);
}

bool ShardedHashTable::find(const uint128_t& key, uint64_t& data) {
    uint32_t shard_idx = getShardIndex(key);
    
    std::lock_guard<std::mutex> lock(shard_mutexes[shard_idx]);
    auto& shard = shards[shard_idx];
    
    lookup_operations.fetch_add(1);
    
    auto it = shard.find(key);
    if (it != shard.end()) {
        data = it->second;
        return true;
    }
    
    return false;
}

// 简化的碰撞检查 - 概念验证版本
bool ShardedHashTable::checkCollision(const uint128_t& key1, const uint128_t& key2) {
    // 简化实现：检查键是否不同
    return !(key1.low == key2.low && key1.high == key2.high);
}

void ShardedHashTable::clear() {
    for (uint32_t i = 0; i < num_shards; i++) {
        std::lock_guard<std::mutex> lock(shard_mutexes[i]);
        shards[i].clear();
        shard_sizes[i].store(0);
        shard_collisions[i].store(0);
    }
    total_items.store(0);
    total_memory_used.store(0);
    insert_operations.store(0);
    lookup_operations.store(0);
    collision_found_count.store(0);
}

ShardStatistics ShardedHashTable::getStatistics() const {
    ShardStatistics stats;
    
    stats.total_items = total_items.load();
    stats.total_collisions = 0;
    stats.memory_used = 0;
    stats.operations_count = insert_operations.load() + lookup_operations.load();
    
    for (uint32_t i = 0; i < num_shards; i++) {
        stats.total_collisions += shard_collisions[i].load();
        stats.memory_used += shard_sizes[i].load() * sizeof(uint64_t);
    }
    
    if (stats.operations_count > 0) {
        stats.collision_rate = (double)stats.total_collisions / (double)stats.operations_count;
    }
    
    size_t total_capacity = num_shards * (ShardConfig::SHARD_SIZE / sizeof(uint64_t));
    if (total_capacity > 0) {
        stats.load_factor = (double)stats.total_items / (double)total_capacity;
    }
    
    return stats;
}

void ShardedHashTable::printStatistics() const {
    auto stats = getStatistics();
    
    std::cout << "=== Sharded Hash Table Statistics ===" << std::endl;
    std::cout << "Total shards: " << num_shards << std::endl;
    std::cout << "Total items: " << stats.total_items << std::endl;
    std::cout << "Memory usage: " << (stats.memory_used / (1024*1024)) << " MB" << std::endl;
    std::cout << "Load factor: " << std::fixed << std::setprecision(4) 
              << (stats.load_factor * 100.0) << "%" << std::endl;
    std::cout << "Collision rate: " << std::fixed << std::setprecision(6) 
              << (stats.collision_rate * 100.0) << "%" << std::endl;
    std::cout << "Total operations: " << stats.operations_count << std::endl;
    std::cout << "Solutions found: " << collision_found_count.load() << std::endl;
    
    // 负载均衡统计
    double load_balance = calculateLoadBalance();
    std::cout << "Load balance: " << std::fixed << std::setprecision(2) 
              << (load_balance * 100.0) << "%" << std::endl;
}

void ShardedHashTable::printDetailedShardInfo() const {
    std::cout << "\n=== Detailed Shard Information ===" << std::endl;
    std::cout << std::setw(6) << "Shard" << std::setw(12) << "Items" 
              << std::setw(12) << "Load%" << std::setw(12) << "Collisions" << std::endl;
    std::cout << std::string(42, '-') << std::endl;
    
    for (uint32_t i = 0; i < num_shards; i++) {
        uint64_t items = shard_sizes[i].load();
        double load_pct = (double)items / (ShardConfig::SHARD_SIZE / sizeof(uint64_t)) * 100.0;
        uint64_t collisions = shard_collisions[i].load();
        
        std::cout << std::setw(6) << i 
                  << std::setw(12) << items
                  << std::setw(11) << std::fixed << std::setprecision(2) << load_pct << "%"
                  << std::setw(12) << collisions << std::endl;
    }
}

double ShardedHashTable::calculateLoadBalance() const {
    if (num_shards == 0) return 1.0;
    
    std::vector<uint64_t> sizes;
    for (uint32_t i = 0; i < num_shards; i++) {
        sizes.push_back(shard_sizes[i].load());
    }
    
    auto minmax = std::minmax_element(sizes.begin(), sizes.end());
    uint64_t min_size = *minmax.first;
    uint64_t max_size = *minmax.second;
    
    if (max_size == 0) return 1.0;
    
    return 1.0 - (double)(max_size - min_size) / (double)max_size;
}

bool ShardedHashTable::needsResize() const {
    auto stats = getStatistics();
    return stats.load_factor > max_load_factor;
}

void ShardedHashTable::setMaxLoadFactor(double factor) {
    max_load_factor = std::max(0.1, std::min(0.9, factor));
}

ShardedHashTable::PerformanceMetrics ShardedHashTable::getPerformanceMetrics() const {
    PerformanceMetrics metrics;
    
    auto stats = getStatistics();
    
    // 简化的性能指标计算
    metrics.insert_rate = insert_operations.load() / 60.0;  // 假设运行1分钟
    metrics.lookup_rate = lookup_operations.load() / 60.0;
    metrics.collision_rate = stats.collision_rate;
    metrics.memory_efficiency = (double)stats.memory_used / (target_memory_gb * 1024 * 1024 * 1024);
    metrics.load_balance = calculateLoadBalance();
    
    return metrics;
}

// ShardedHashTableFactory实现
std::unique_ptr<ShardedHashTable> ShardedHashTableFactory::createOptimal(uint32_t range_bits, 
                                                                         uint64_t kangaroo_count) {
    // 基于搜索范围和袋鼠数量计算最优配置
    size_t estimated_memory = std::max((size_t)4, (size_t)(range_bits / 4));  // 启发式估算
    estimated_memory = std::min(estimated_memory, (size_t)256);     // 最大256GB
    
    uint32_t optimal_shards = (uint32_t)((estimated_memory + 3) / 4);      // 每4GB一个分片
    
    return std::make_unique<ShardedHashTable>(estimated_memory, optimal_shards);
}

std::unique_ptr<ShardedHashTable> ShardedHashTableFactory::createMemoryLimited(size_t max_memory_gb) {
    return std::make_unique<ShardedHashTable>(max_memory_gb);
}

std::unique_ptr<ShardedHashTable> ShardedHashTableFactory::createHighPerformance(uint64_t target_ops_per_sec) {
    // 基于目标操作速率计算配置
    size_t memory_gb = std::max((size_t)16, (size_t)(target_ops_per_sec / 1000000));  // 启发式
    uint32_t shards = (uint32_t)(memory_gb / 2);  // 更多分片提高并发性
    
    auto table = std::make_unique<ShardedHashTable>(memory_gb, shards);
    table->setMaxLoadFactor(0.6);  // 较低负载因子提高性能
    
    return table;
}
