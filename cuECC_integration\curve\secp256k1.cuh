#ifndef _SECP256K1_CUH
#define _SECP256K1_CUH

#include "point.cuh"

// 🔧 修复：添加CUDA兼容性宏
#ifndef __CUDACC__
#define __constant__ const
#define __device__
#define __host__
#endif

// 🔧 常量声明（定义在secp256k1_constants.cu中）
extern __constant__ u64 A[4];
extern __constant__ u64 B[4];
extern __constant__ u64 P[4];
extern __constant__ cuECC_Point_Internal G;

// 🔧 修复：使用正确的类型名称
__device__ inline void secp256k1Add(cuECC_Point_Internal *output, const cuECC_Point_Internal *p, const cuECC_Point_Internal *q) {
  pointAdd(output, p, q, P, A, B);
}

__device__ inline void secp256k1Sub(cuECC_Point_Internal *output, const cuECC_Point_Internal *p, const cuECC_Point_Internal *q) {
  pointSub(output, p, q, P, A, B);
}

__device__ inline void secp256k1Neg(cuECC_Point_Internal *output, const cuECC_Point_Internal *p) {
  pointNeg(output, p, P);
}

__device__ inline void secp256k1Double(cuECC_Point_Internal *output, const cuECC_Point_Internal *p) {
  pointDouble(output, p, P, A, B);
}

__device__ inline void secp256k1Mul(cuECC_Point_Internal *output, const cuECC_Point_Internal *p, const u64 scalar[4]) {
  pointMul(output, p, scalar, P, A, B);
}

// 🎯 核心函数：secp256k1椭圆曲线点乘法 - 计算 scalar * G
__device__ inline void secp256k1PublicKey(cuECC_Point_Internal *output, const u64 privateKey[4]) {
  secp256k1Mul(output, &G, privateKey);
}

#endif
