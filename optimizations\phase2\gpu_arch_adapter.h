#ifndef GPU_ARCH_ADAPTER_H
#define GPU_ARCH_ADAPTER_H

#include <cuda_runtime.h>
#include <vector>
#include <memory>
#include <string>

/**
 * @file gpu_arch_adapter.h
 * @brief GPU架构适配器 - 为不同GPU架构提供最优配置
 * 
 * 支持从Maxwell 2.0到Hopper的全系列NVIDIA GPU
 * 自动配置Per-SM分块参数和内核启动配置
 */

/**
 * @brief GPU架构配置结构
 */
struct GPUArchConfig {
    int major;                              // 计算能力主版本
    int minor;                              // 计算能力次版本
    std::string arch_name;                  // 架构名称
    
    // 硬件特性
    uint32_t max_blocks_per_sm;             // 每SM最大块数
    uint32_t max_threads_per_block;         // 每块最大线程数
    uint32_t max_threads_per_sm;            // 每SM最大线程数
    uint32_t shared_memory_per_block;       // 每块共享内存(字节)
    uint32_t shared_memory_per_sm;          // 每SM共享内存(字节)
    uint32_t warp_size;                     // Warp大小
    
    // 优化配置
    uint32_t optimal_blocks_per_sm;         // 推荐每SM块数
    uint32_t optimal_threads_per_block;     // 推荐每块线程数
    uint32_t kangaroos_per_sm;              // 每SM袋鼠数
    uint32_t kangaroos_per_thread;          // 每线程袋鼠数
    uint32_t shared_memory_usage;           // 共享内存使用量
    
    // 特性支持
    bool supports_cooperative_groups;       // 协作组支持
    bool supports_unified_memory;           // 统一内存支持
    bool supports_cuda_graphs;              // CUDA图支持
    bool supports_async_memory;             // 异步内存支持
    
    // 性能调优
    int32_t optimal_dp_adjustment;          // DP位数调整（可为负数）
    double memory_usage_ratio;              // 推荐内存使用比例
    uint32_t steps_per_kernel;              // 每次内核调用的步数
};

/**
 * @brief 内核启动配置
 */
struct KernelLaunchConfig {
    dim3 grid_size;                         // 网格大小
    dim3 block_size;                        // 块大小
    size_t shared_memory_size;              // 动态共享内存大小
    cudaStream_t stream;                    // CUDA流
    uint32_t total_kangaroos;               // 总袋鼠数
    uint32_t kangaroos_per_block;           // 每块袋鼠数
};

/**
 * @brief GPU架构适配器类
 */
class GPUArchAdapter {
private:
    static std::vector<GPUArchConfig> arch_configs;
    static bool configs_initialized;
    
public:
    /**
     * @brief 初始化架构配置
     */
    static void initializeConfigs();
    
    /**
     * @brief 获取GPU的最优配置
     * @param device_id GPU设备ID
     * @return GPU架构配置
     */
    static GPUArchConfig getOptimalConfig(int device_id);
    
    /**
     * @brief 根据GPU配置计算内核启动参数
     * @param config GPU架构配置
     * @param total_kangaroos 总袋鼠数
     * @param available_memory 可用GPU内存
     * @return 内核启动配置
     */
    static KernelLaunchConfig calculateKernelConfig(const GPUArchConfig& config,
                                                   uint32_t total_kangaroos,
                                                   size_t available_memory);
    
    /**
     * @brief 计算最优袋鼠数量
     * @param device_id GPU设备ID
     * @param available_memory 可用内存
     * @param range_bits 搜索范围位数
     * @return 推荐袋鼠数量
     */
    static uint32_t calculateOptimalKangaroos(int device_id, 
                                             size_t available_memory,
                                             uint32_t range_bits);
    
    /**
     * @brief 获取架构特定的性能调优参数
     * @param config GPU架构配置
     * @return 调优参数
     */
    static struct PerformanceTuning {
        uint32_t steps_per_kernel;          // 每次内核调用步数
        uint32_t kernels_per_round;         // 每轮内核调用次数
        uint32_t memory_coalescing_factor;  // 内存合并因子
        bool use_texture_memory;            // 是否使用纹理内存
        bool use_constant_memory;           // 是否使用常量内存
    } getPerformanceTuning(const GPUArchConfig& config);
    
    /**
     * @brief 验证内核配置的有效性
     * @param config 内核启动配置
     * @param gpu_config GPU架构配置
     * @return 是否有效
     */
    static bool validateKernelConfig(const KernelLaunchConfig& config,
                                   const GPUArchConfig& gpu_config);
    
    /**
     * @brief 打印GPU配置信息
     * @param config GPU架构配置
     */
    static void printArchConfig(const GPUArchConfig& config);
    
    /**
     * @brief 打印内核配置信息
     * @param config 内核启动配置
     */
    static void printKernelConfig(const KernelLaunchConfig& config);
    
    /**
     * @brief 获取架构名称
     * @param major 计算能力主版本
     * @param minor 计算能力次版本
     * @return 架构名称
     */
    static std::string getArchitectureName(int major, int minor);
    
    /**
     * @brief 检查GPU是否支持特定特性
     * @param device_id GPU设备ID
     * @param feature 特性名称
     * @return 是否支持
     */
    static bool supportsFeature(int device_id, const std::string& feature);
    
    /**
     * @brief 获取所有支持的GPU架构列表
     * @return 架构配置列表
     */
    static std::vector<GPUArchConfig> getSupportedArchitectures();
    
private:
    /**
     * @brief 创建特定架构的配置
     * @param major 计算能力主版本
     * @param minor 计算能力次版本
     * @return GPU架构配置
     */
    static GPUArchConfig createArchConfig(int major, int minor);
    
    /**
     * @brief 计算Maxwell架构配置
     * @param minor 次版本号
     * @return GPU架构配置
     */
    static GPUArchConfig createMaxwellConfig(int minor);
    
    /**
     * @brief 计算Pascal架构配置
     * @param minor 次版本号
     * @return GPU架构配置
     */
    static GPUArchConfig createPascalConfig(int minor);
    
    /**
     * @brief 计算Volta/Turing架构配置
     * @param minor 次版本号
     * @return GPU架构配置
     */
    static GPUArchConfig createVoltaTuringConfig(int minor);
    
    /**
     * @brief 计算Ampere/Ada架构配置
     * @param minor 次版本号
     * @return GPU架构配置
     */
    static GPUArchConfig createAmpereAdaConfig(int minor);
    
    /**
     * @brief 计算Hopper架构配置
     * @param minor 次版本号
     * @return GPU架构配置
     */
    static GPUArchConfig createHopperConfig(int minor);
    
    /**
     * @brief 优化块大小和网格大小
     * @param config GPU架构配置
     * @param total_kangaroos 总袋鼠数
     * @return 优化后的启动配置
     */
    static KernelLaunchConfig optimizeGridAndBlock(const GPUArchConfig& config,
                                                  uint32_t total_kangaroos);
    
    /**
     * @brief 计算共享内存使用量
     * @param config GPU架构配置
     * @param threads_per_block 每块线程数
     * @return 共享内存使用量
     */
    static size_t calculateSharedMemoryUsage(const GPUArchConfig& config,
                                            uint32_t threads_per_block);
};

/**
 * @brief GPU性能监控器
 */
class GPUPerformanceMonitor {
private:
    cudaEvent_t start_event, stop_event;
    float last_kernel_time;
    uint64_t total_operations;
    uint64_t total_time_ms;
    
public:
    GPUPerformanceMonitor();
    ~GPUPerformanceMonitor();
    
    /**
     * @brief 开始性能测量
     */
    void startMeasurement();
    
    /**
     * @brief 结束性能测量
     * @return 内核执行时间(毫秒)
     */
    float endMeasurement();
    
    /**
     * @brief 更新操作统计
     * @param operations 操作数
     */
    void updateOperations(uint64_t operations);
    
    /**
     * @brief 获取平均性能
     * @return 每秒操作数
     */
    double getAveragePerformance() const;
    
    /**
     * @brief 获取最近的内核时间
     * @return 内核时间(毫秒)
     */
    float getLastKernelTime() const { return last_kernel_time; }
    
    /**
     * @brief 重置统计信息
     */
    void reset();
};

#endif // GPU_ARCH_ADAPTER_H
