/*
* This file is part of the VanitySearch distribution (https://github.com/JeanLucPons/Kangaroo).
* Copyright (c) 2019 <PERSON>ONS.
*
* This program is free software: you can redistribute it and/or modify
* it under the terms of the GNU General Public License as published by
* the Free Software Foundation, version 3.
*
* This program is distributed in the hope that it will be useful, but
* WITHOUT ANY WARRANTY; without even the implied warranty of
* MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
* General Public License for more details.
*
* You should have received a copy of the GNU General Public License
* along with this program. If not, see <http://www.gnu.org/licenses/>.
*/

#include "GPUEngine.h"
#include <math.h>

using namespace std;


void GPUEngine::GenerateCode(Secp256K1 *secp) {

  // Compute generator table
  Int jumpDistance[129];
  Point jumpPoint[129];

  // Kangaroo jumps
  jumpPoint[0] = secp->G;
  jumpDistance[0].SetInt32(1);
  for(int i = 1; i < 129; ++i) {
    jumpDistance[i].Add(&jumpDistance[i - 1],&jumpDistance[i - 1]);
    jumpPoint[i] = secp->DoubleDirect(jumpPoint[i - 1]);
  }

  // Write file
  FILE *f = fopen("../GPU/GPUGroup.h","w");

  fprintf(f,"// File generated by GPUEngine::GenerateCode()\n");

  fprintf(f,"// Jump distance table (Contains 1,2,4,...,2^129\n");
  fprintf(f,"__device__ __constant__ uint64_t jD[][4] = {\n");
  for(int i = 0; i < 129; i++) {
    fprintf(f,"  %s,\n",jumpDistance[i].GetC64Str(4).c_str());
  }
  fprintf(f,"};\n");

  fprintf(f,"// Jump point table (Contains G,2G,4G,...,2^129.G)\n");
  fprintf(f,"__device__ __constant__ uint64_t jPx[][4] = {\n");
  for(int i = 0; i < 129; i++) {
    fprintf(f,"  %s,\n",jumpPoint[i].x.GetC64Str(4).c_str());
  }
  fprintf(f,"};\n");

  fprintf(f,"__device__ __constant__ uint64_t jPy[][4] = {\n");
  for(int i = 0; i < 129; i++) {
    fprintf(f,"  %s,\n",jumpPoint[i].y.GetC64Str(4).c_str());
  }
  fprintf(f,"};\n\n");

  fclose(f);

}
