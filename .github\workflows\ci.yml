name: CI/CD for <PERSON><PERSON>

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - name: Install CUDA
      run: |
        wget https://developer.download.nvidia.com/compute/cuda/repos/ubuntu2004/x86_64/cuda-keyring_1.0-1_all.deb
        sudo dpkg -i cuda-keyring_1.0-1_all.deb
        sudo apt-get update
        sudo apt-get install -y cuda-12-2
    - name: Set up CMake
      uses: lukka/get-cmake@v3.21.2
    - name: Build
      run: |
        mkdir build
        cd build
        cmake ..
        make
    - name: Test
      run: ./kangaroo --test