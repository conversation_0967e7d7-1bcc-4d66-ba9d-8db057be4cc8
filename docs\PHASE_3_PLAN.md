# 📋 Phase 3: 内存系统重构详细计划

## 🎯 目标概述

突破单一哈希表内存限制，实现分片哈希表系统，支持100-bit+范围，优化内存访问模式。

## 🔧 核心技术实现

### 3.1 分片哈希表系统

#### sharded_hashtable.h
```cpp
#ifndef SHARDED_HASHTABLE_H
#define SHARDED_HASHTABLE_H

#include <unordered_map>
#include <vector>
#include <memory>
#include <mutex>

// 128-bit键类型
struct uint128_t {
    uint64_t low;
    uint64_t high;
    
    bool operator==(const uint128_t& other) const {
        return low == other.low && high == other.high;
    }
};

// 128-bit哈希函数
struct uint128_hash {
    size_t operator()(const uint128_t& key) const {
        return std::hash<uint64_t>{}(key.low) ^ 
               (std::hash<uint64_t>{}(key.high) << 1);
    }
};

// 分片配置
struct ShardConfig {
    static const size_t SHARD_SIZE = 4ULL * 1024 * 1024 * 1024;  // 4GB per shard
    static const uint32_t MAX_SHARDS = 64;  // 最大64个分片 (256GB)
    static const uint32_t ITEMS_PER_SHARD = SHARD_SIZE / sizeof(ITEM);
};

class ShardedHashTable {
private:
    std::vector<std::unordered_map<uint128_t, ITEM, uint128_hash>> shards;
    std::vector<std::mutex> shard_mutexes;
    uint32_t num_shards;
    size_t total_items;
    size_t total_memory_used;
    
    // 统计信息
    std::vector<uint64_t> shard_sizes;
    std::vector<uint64_t> shard_collisions;
    
public:
    ShardedHashTable(size_t target_memory_gb);
    ~ShardedHashTable();
    
    // 核心操作
    bool insert(const uint128_t& key, const ITEM& item);
    bool find(const uint128_t& key, ITEM& item);
    void clear();
    
    // 分片管理
    uint32_t getShardIndex(const uint128_t& key) const;
    void balanceShards();
    
    // 统计信息
    size_t getTotalItems() const { return total_items; }
    size_t getMemoryUsage() const { return total_memory_used; }
    void printStatistics() const;
    
    // 碰撞检测
    bool checkCollision(const ITEM& item1, const ITEM& item2, uint256_t& private_key);
    
private:
    uint128_t makeKey(const uint256_t& point_x) const;
    void updateStatistics();
};

#endif // SHARDED_HASHTABLE_H
```

#### sharded_hashtable.cpp
```cpp
#include "sharded_hashtable.h"
#include <iostream>
#include <iomanip>
#include <algorithm>

ShardedHashTable::ShardedHashTable(size_t target_memory_gb) {
    // 计算分片数量
    size_t target_memory = target_memory_gb * 1024ULL * 1024 * 1024;
    num_shards = std::min((uint32_t)((target_memory + ShardConfig::SHARD_SIZE - 1) / ShardConfig::SHARD_SIZE),
                         ShardConfig::MAX_SHARDS);
    
    // 初始化分片
    shards.resize(num_shards);
    shard_mutexes.resize(num_shards);
    shard_sizes.resize(num_shards, 0);
    shard_collisions.resize(num_shards, 0);
    
    total_items = 0;
    total_memory_used = 0;
    
    // 预分配内存
    for (uint32_t i = 0; i < num_shards; i++) {
        shards[i].reserve(ShardConfig::ITEMS_PER_SHARD / 4);  // 预留25%负载因子
    }
    
    std::cout << "Initialized " << num_shards << " shards, target memory: " 
              << target_memory_gb << " GB" << std::endl;
}

ShardedHashTable::~ShardedHashTable() {
    clear();
}

uint32_t ShardedHashTable::getShardIndex(const uint128_t& key) const {
    // 使用高质量哈希函数分布键
    uint64_t hash = key.low ^ (key.high << 1) ^ (key.high >> 1);
    return hash % num_shards;
}

uint128_t ShardedHashTable::makeKey(const uint256_t& point_x) const {
    uint128_t key;
    key.low = point_x.d[0];
    key.high = point_x.d[1];
    return key;
}

bool ShardedHashTable::insert(const uint128_t& key, const ITEM& item) {
    uint32_t shard_idx = getShardIndex(key);
    
    std::lock_guard<std::mutex> lock(shard_mutexes[shard_idx]);
    auto& shard = shards[shard_idx];
    
    // 检查是否已存在
    auto it = shard.find(key);
    if (it != shard.end()) {
        // 发现碰撞!
        uint256_t private_key;
        if (checkCollision(it->second, item, private_key)) {
            std::cout << "COLLISION FOUND!" << std::endl;
            std::cout << "Private key: ";
            uint256_print(&private_key);
            return true;  // 找到解
        }
        shard_collisions[shard_idx]++;
        return false;  // 假碰撞
    }
    
    // 插入新项
    shard[key] = item;
    shard_sizes[shard_idx]++;
    total_items++;
    
    return false;
}

bool ShardedHashTable::find(const uint128_t& key, ITEM& item) {
    uint32_t shard_idx = getShardIndex(key);
    
    std::lock_guard<std::mutex> lock(shard_mutexes[shard_idx]);
    auto& shard = shards[shard_idx];
    
    auto it = shard.find(key);
    if (it != shard.end()) {
        item = it->second;
        return true;
    }
    
    return false;
}

bool ShardedHashTable::checkCollision(const ITEM& item1, const ITEM& item2, uint256_t& private_key) {
    // 检查是否为不同类型的袋鼠 (野生 vs 驯服)
    if (item1.kType != item2.kType) {
        // 计算私钥: |d1 - d2|
        if (item1.kType == TAME) {
            // 驯服袋鼠 - 野生袋鼠
            if (uint256_compare(&item2.d, &item1.d) > 0) {
                uint256_sub(&private_key, &item2.d, &item1.d);
            } else {
                uint256_sub(&private_key, &item1.d, &item2.d);
            }
        } else {
            // 野生袋鼠 - 驯服袋鼠
            if (uint256_compare(&item1.d, &item2.d) > 0) {
                uint256_sub(&private_key, &item1.d, &item2.d);
            } else {
                uint256_sub(&private_key, &item2.d, &item1.d);
            }
        }
        return true;
    }
    
    return false;  // 同类型袋鼠碰撞，继续搜索
}

void ShardedHashTable::printStatistics() const {
    std::cout << "=== Sharded Hash Table Statistics ===" << std::endl;
    std::cout << "Total shards: " << num_shards << std::endl;
    std::cout << "Total items: " << total_items << std::endl;
    std::cout << "Memory usage: " << (total_memory_used / (1024*1024)) << " MB" << std::endl;
    
    // 分片负载分布
    std::cout << "\nShard load distribution:" << std::endl;
    for (uint32_t i = 0; i < num_shards; i++) {
        double load_factor = (double)shard_sizes[i] / ShardConfig::ITEMS_PER_SHARD;
        std::cout << "  Shard " << std::setw(2) << i << ": " 
                  << std::setw(8) << shard_sizes[i] << " items ("
                  << std::fixed << std::setprecision(2) << (load_factor * 100.0) << "%), "
                  << shard_collisions[i] << " collisions" << std::endl;
    }
    
    // 负载均衡统计
    auto minmax = std::minmax_element(shard_sizes.begin(), shard_sizes.end());
    double imbalance = (*minmax.second > 0) ? 
        (double)(*minmax.second - *minmax.first) / *minmax.second : 0.0;
    std::cout << "\nLoad balance: " << std::fixed << std::setprecision(2) 
              << ((1.0 - imbalance) * 100.0) << "%" << std::endl;
}

void ShardedHashTable::clear() {
    for (uint32_t i = 0; i < num_shards; i++) {
        std::lock_guard<std::mutex> lock(shard_mutexes[i]);
        shards[i].clear();
        shard_sizes[i] = 0;
        shard_collisions[i] = 0;
    }
    total_items = 0;
    total_memory_used = 0;
}
```

### 3.2 GPU内存池管理

#### gpu_memory_pool.h
```cpp
#ifndef GPU_MEMORY_POOL_H
#define GPU_MEMORY_POOL_H

#include <cuda_runtime.h>
#include <vector>
#include <memory>
#include <mutex>

struct MemoryBlock {
    void* ptr;
    size_t size;
    bool in_use;
    cudaStream_t stream;
    int device_id;
};

class GPUMemoryPool {
private:
    std::vector<std::unique_ptr<MemoryBlock>> blocks;
    std::mutex pool_mutex;
    size_t total_allocated;
    size_t peak_usage;
    int device_id;
    
    // 内存统计
    size_t allocation_count;
    size_t deallocation_count;
    size_t reuse_count;
    
public:
    GPUMemoryPool(int device_id);
    ~GPUMemoryPool();
    
    // 内存分配
    void* allocate(size_t size, cudaStream_t stream = 0);
    void deallocate(void* ptr);
    
    // 异步内存操作
    void* allocateAsync(size_t size, cudaStream_t stream);
    void deallocateAsync(void* ptr, cudaStream_t stream);
    
    // 内存池管理
    void cleanup();
    void defragment();
    
    // 统计信息
    size_t getTotalAllocated() const { return total_allocated; }
    size_t getPeakUsage() const { return peak_usage; }
    void printStatistics() const;
    
private:
    MemoryBlock* findFreeBlock(size_t size);
    void updateStatistics();
};

#endif // GPU_MEMORY_POOL_H
```

### 3.3 异步内存传输系统

#### async_memory_transfer.h
```cpp
#ifndef ASYNC_MEMORY_TRANSFER_H
#define ASYNC_MEMORY_TRANSFER_H

#include <cuda_runtime.h>
#include <vector>
#include <queue>
#include <thread>
#include <atomic>

struct TransferRequest {
    void* src;
    void* dst;
    size_t size;
    cudaMemcpyKind kind;
    cudaStream_t stream;
    bool completed;
};

class AsyncMemoryTransfer {
private:
    std::vector<cudaStream_t> streams;
    std::queue<TransferRequest> transfer_queue;
    std::mutex queue_mutex;
    std::atomic<bool> running;
    std::thread transfer_thread;
    
    // 性能统计
    std::atomic<uint64_t> total_transfers;
    std::atomic<uint64_t> total_bytes;
    std::atomic<double> total_time;
    
public:
    AsyncMemoryTransfer(int num_streams = 4);
    ~AsyncMemoryTransfer();
    
    // 异步传输接口
    void transferAsync(void* src, void* dst, size_t size, 
                      cudaMemcpyKind kind, cudaStream_t stream = 0);
    
    // 批量传输
    void transferBatch(const std::vector<TransferRequest>& requests);
    
    // 同步等待
    void waitForCompletion();
    void waitForStream(cudaStream_t stream);
    
    // 性能监控
    double getBandwidth() const;
    void printPerformanceStats() const;
    
private:
    void transferWorker();
    cudaStream_t getOptimalStream();
};

#endif // ASYNC_MEMORY_TRANSFER_H
```

## 📊 内存优化目标

### 内存容量提升
- 单机内存支持: 从6GB提升到256GB
- 分片数量: 最多64个分片
- 负载均衡: >95%均匀分布

### 内存访问优化
- 缓存命中率: 提升80%
- 内存带宽利用率: 提升5倍
- 异步传输效率: 提升3倍

### 支持范围扩展
- 当前支持: 80-bit
- Phase 3目标: 100-bit+
- 理论极限: 120-bit

## 🧪 验证测试

### 功能测试
- [ ] 分片哈希表正确性
- [ ] 内存池稳定性
- [ ] 异步传输可靠性
- [ ] 大范围搜索测试

### 性能测试
- [ ] 内存使用效率 >90%
- [ ] 分片负载均衡 >95%
- [ ] 异步传输带宽 >80%峰值
- [ ] 100-bit范围支持验证

## 📁 文件结构

```
optimizations/phase3/
├── sharded_hashtable.h
├── sharded_hashtable.cpp
├── gpu_memory_pool.h
├── gpu_memory_pool.cu
├── async_memory_transfer.h
├── async_memory_transfer.cu
└── memory_benchmark.cpp
```
