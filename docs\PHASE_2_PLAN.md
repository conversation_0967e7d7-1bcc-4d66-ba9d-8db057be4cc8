# 📋 Phase 2: GPU架构优化详细计划

## 🎯 目标概述

实现Per-SM分块内核，自适应DP计算，最大化GPU资源利用率，支持全系列NVIDIA GPU架构。

## 🔧 核心技术实现

### 2.1 Per-SM分块内核

#### kang_block_kernel.cu
```cpp
#include <cooperative_groups.h>
using namespace cooperative_groups;

// 每个SM独立的袋鼠内核
__global__ __launch_bounds__(1024, 2)
void kang_per_sm_kernel(
    uint64_t* kangaroos,        // 袋鼠状态 [num_kangaroos * 12]
    uint64_t* jump_distances,   // 跳跃距离表 [32 * 4]
    uint64_t* jump_points_x,    // 跳跃点X坐标 [32 * 4]
    uint64_t* jump_points_y,    // 跳跃点Y坐标 [32 * 4]
    uint64_t dp_mask,           // DP掩码
    uint32_t* found_count,      // 发现计数
    ITEM* found_items,          // 发现的项目
    uint32_t sm_id,             // SM ID
    uint32_t kangaroos_per_sm   // 每SM袋鼠数
) {
    // 共享内存优化
    __shared__ uint64_t smem_jump_dist[32][4];
    __shared__ uint64_t smem_jump_x[32][4];
    __shared__ uint64_t smem_jump_y[32][4];
    __shared__ uint32_t smem_found_count;
    __shared__ ITEM smem_found_items[16];  // 每块最多16个发现
    
    int tid = threadIdx.x;
    int bid = blockIdx.x;
    int local_kang_id = tid + bid * blockDim.x;
    int global_kang_id = local_kang_id + sm_id * kangaroos_per_sm;
    
    // 初始化共享内存
    if (tid == 0) {
        smem_found_count = 0;
    }
    
    // 加载跳跃表到共享内存 (合并访问)
    if (tid < 32) {
        for (int i = 0; i < 4; i++) {
            smem_jump_dist[tid][i] = jump_distances[tid * 4 + i];
            smem_jump_x[tid][i] = jump_points_x[tid * 4 + i];
            smem_jump_y[tid][i] = jump_points_y[tid * 4 + i];
        }
    }
    __syncthreads();
    
    // 加载袋鼠状态
    uint64_t kang_x[4], kang_y[4], kang_d[4];
    uint32_t kang_type;
    
    if (global_kang_id < total_kangaroos) {
        load_kangaroo_state(kangaroos, global_kang_id, kang_x, kang_y, kang_d, &kang_type);
        
        // 袋鼠随机游走
        for (int step = 0; step < STEPS_PER_KERNEL; step++) {
            // 计算跳跃索引 (基于X坐标低5位)
            uint32_t jump_idx = kang_x[0] & 0x1F;
            
            // 椭圆曲线点加法 (使用共享内存)
            point_add_256(kang_x, kang_y, 
                         smem_jump_x[jump_idx], smem_jump_y[jump_idx]);
            
            // 距离累加
            uint256_add(kang_d, kang_d, smem_jump_dist[jump_idx]);
            
            // 检查DP (Distinguished Point)
            if ((kang_x[0] & dp_mask) == 0) {
                uint32_t local_idx = atomicAdd(&smem_found_count, 1);
                if (local_idx < 16) {
                    // 存储到共享内存
                    store_found_item(&smem_found_items[local_idx], 
                                   kang_x, kang_y, kang_d, kang_type);
                }
            }
        }
        
        // 保存袋鼠状态
        store_kangaroo_state(kangaroos, global_kang_id, kang_x, kang_y, kang_d, kang_type);
    }
    
    __syncthreads();
    
    // 将共享内存结果写回全局内存
    if (tid == 0 && smem_found_count > 0) {
        uint32_t global_offset = atomicAdd(found_count, smem_found_count);
        for (uint32_t i = 0; i < smem_found_count && i < 16; i++) {
            found_items[global_offset + i] = smem_found_items[i];
        }
    }
}
```

### 2.2 自适应DP计算系统

#### adaptive_dp.h
```cpp
#ifndef ADAPTIVE_DP_H
#define ADAPTIVE_DP_H

#include <cmath>
#include <algorithm>

class AdaptiveDP {
private:
    uint32_t current_dp_bits;
    uint64_t total_kangaroos;
    uint64_t range_size_bits;
    double collision_rate;
    uint64_t total_operations;
    
public:
    AdaptiveDP();
    
    // 计算最优DP位数
    uint32_t calculateOptimalDP(const uint256_t& range_start, 
                               const uint256_t& range_end,
                               uint64_t num_kangaroos);
    
    // 动态调整DP
    void adjustDP(double current_collision_rate, uint64_t operations);
    
    // 获取当前DP掩码
    uint64_t getDPMask() const;
    
    // 性能统计
    void updateStatistics(uint64_t new_operations, uint32_t new_collisions);
    
    // 打印DP信息
    void printDPInfo() const;
};

#endif // ADAPTIVE_DP_H
```

#### adaptive_dp.cpp
```cpp
#include "adaptive_dp.h"
#include <iostream>
#include <iomanip>

AdaptiveDP::AdaptiveDP() 
    : current_dp_bits(20), total_kangaroos(0), range_size_bits(0),
      collision_rate(0.0), total_operations(0) {}

uint32_t AdaptiveDP::calculateOptimalDP(const uint256_t& range_start, 
                                       const uint256_t& range_end,
                                       uint64_t num_kangaroos) {
    // 计算范围大小
    uint256_t range_size;
    uint256_sub(&range_size, &range_end, &range_start);
    range_size_bits = uint256_bitlength(&range_size);
    total_kangaroos = num_kangaroos;
    
    // 理论最优DP计算: log2(sqrt(N)/kangaroos)
    // 其中N是搜索空间大小
    double search_space = pow(2.0, (double)range_size_bits);
    double expected_steps = sqrt(search_space * M_PI / 4.0);  // Pollard's kangaroo理论
    double dp_density = expected_steps / (double)num_kangaroos;
    
    // 转换为DP位数
    double optimal_dp_bits = log2(dp_density);
    
    // 限制在合理范围内
    uint32_t dp_bits = (uint32_t)std::max(12.0, std::min(32.0, optimal_dp_bits));
    
    // 根据GPU架构调整
    int device_id;
    cudaGetDevice(&device_id);
    cudaDeviceProp prop;
    cudaGetDeviceProperties(&prop, device_id);
    
    // 老架构适当增加DP位数 (减少内存压力)
    if (prop.major < 7) {
        dp_bits += 2;
    } else if (prop.major >= 8) {
        dp_bits -= 1;  // 新架构可以处理更多DP
    }
    
    current_dp_bits = std::max(12u, std::min(32u, dp_bits));
    
    std::cout << "Adaptive DP Calculation:" << std::endl;
    std::cout << "  Range: 2^" << range_size_bits << " bits" << std::endl;
    std::cout << "  Kangaroos: " << num_kangaroos << std::endl;
    std::cout << "  GPU: " << prop.name << " (SM " << prop.major << "." << prop.minor << ")" << std::endl;
    std::cout << "  Optimal DP: " << current_dp_bits << " bits" << std::endl;
    
    return current_dp_bits;
}

void AdaptiveDP::adjustDP(double current_collision_rate, uint64_t operations) {
    collision_rate = current_collision_rate;
    total_operations = operations;
    
    // 动态调整策略
    if (collision_rate > 0.15) {
        // 碰撞率过高，增加DP位数
        if (current_dp_bits < 32) {
            current_dp_bits++;
            std::cout << "DP adjusted up to " << current_dp_bits << " bits (high collision rate)" << std::endl;
        }
    } else if (collision_rate < 0.01 && total_operations > 1000000) {
        // 碰撞率过低，减少DP位数
        if (current_dp_bits > 12) {
            current_dp_bits--;
            std::cout << "DP adjusted down to " << current_dp_bits << " bits (low collision rate)" << std::endl;
        }
    }
}

uint64_t AdaptiveDP::getDPMask() const {
    return (1ULL << current_dp_bits) - 1;
}

void AdaptiveDP::updateStatistics(uint64_t new_operations, uint32_t new_collisions) {
    total_operations += new_operations;
    if (total_operations > 0) {
        collision_rate = (double)new_collisions / (double)total_operations;
    }
}

void AdaptiveDP::printDPInfo() const {
    std::cout << "=== Adaptive DP Status ===" << std::endl;
    std::cout << "Current DP bits: " << current_dp_bits << std::endl;
    std::cout << "DP mask: 0x" << std::hex << getDPMask() << std::dec << std::endl;
    std::cout << "Collision rate: " << std::fixed << std::setprecision(4) 
              << (collision_rate * 100.0) << "%" << std::endl;
    std::cout << "Total operations: " << total_operations << std::endl;
}
```

### 2.3 GPU架构适配层

#### gpu_arch_adapter.h
```cpp
#ifndef GPU_ARCH_ADAPTER_H
#define GPU_ARCH_ADAPTER_H

#include <cuda_runtime.h>

struct GPUArchConfig {
    int major;
    int minor;
    uint32_t max_blocks_per_sm;
    uint32_t max_threads_per_block;
    uint32_t shared_memory_per_block;
    uint32_t kangaroos_per_sm;
    uint32_t optimal_block_size;
    bool supports_cooperative_groups;
};

class GPUArchAdapter {
public:
    static GPUArchConfig getOptimalConfig(int device_id);
    static void configureKernelLaunch(int device_id, 
                                     dim3& grid_size, 
                                     dim3& block_size,
                                     uint32_t total_kangaroos);
    static uint32_t calculateOptimalKangaroos(int device_id, size_t available_memory);
    static void printArchConfig(const GPUArchConfig& config);
};

#endif // GPU_ARCH_ADAPTER_H
```

## 📊 性能优化目标

### 各GPU架构预期提升

| GPU架构 | 基础性能 | Phase 2性能 | 提升倍数 |
|---------|----------|-------------|----------|
| Maxwell (SM 5.2) | 1.0x | 2.0x | 2.0x |
| Pascal (SM 6.0-6.1) | 1.0x | 2.5x | 2.5x |
| Volta (SM 7.0) | 1.0x | 3.0x | 3.0x |
| Turing (SM 7.5) | 1.0x | 3.5x | 3.5x |
| Ampere (SM 8.0-8.6) | 1.0x | 4.0x | 4.0x |
| Ada (SM 8.9) | 1.0x | 4.5x | 4.5x |
| Hopper (SM 9.0) | 1.0x | 5.0x | 5.0x |

### 内存优化
- 共享内存使用率: 提升80%
- 全局内存访问: 减少60%
- 内存带宽利用率: 提升3倍

## 🧪 验证测试

### 功能测试
- [ ] 所有GPU架构内核正常运行
- [ ] 自适应DP计算正确
- [ ] 已知私钥测试通过
- [ ] 长时间稳定性测试

### 性能测试
- [ ] 32-bit范围: 提升2-5倍
- [ ] 64-bit范围: 提升1.5-3倍
- [ ] GPU利用率: >90%
- [ ] 内存效率: 提升3倍

## 📁 文件结构

```
optimizations/phase2/
├── kang_block_kernel.cu
├── adaptive_dp.h
├── adaptive_dp.cpp
├── gpu_arch_adapter.h
├── gpu_arch_adapter.cu
├── shared_memory_opt.cuh
└── performance_monitor.h
```
