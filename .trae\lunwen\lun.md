# Computing small discrete logarithms faster

**<PERSON>,² and <PERSON><PERSON>**

¹ Department of Computer Science
University of Illinois at Chicago, Chicago, IL 60607-7053, USA
<EMAIL>

² Department of Mathematics and Computer Science
Technische Universiteit Eindhoven, P.O. Box 513, 5600 MB Eindhoven, the Netherlands
<EMAIL>

**Abstract.** Computations of small discrete logarithms are feasible even in "secure" groups, and are used as subroutines in several cryptographic protocols in the literature. For example, the Boneh-Goh-Nissim degree-2-homomorphic public-key encryption system uses generic square-root discrete-logarithm methods for decryption. This paper shows how to use a small group-specific table to accelerate these subroutines. The cost of setting up the table grows with the table size, but the acceleration also grows with the table size. This paper shows experimentally that computing a discrete logarithm in an interval of order l takes only 1.93 · l^(1/3) multiplications on average using a table of size l^(1/3) precomputed with 1.21 · l^(2/3) multiplications, and computing a discrete logarithm in a group of order l takes only 1.77 · ℓ^(1/3) multiplications on average using a table of size ℓ^(1/3) precomputed with 1.24 · ℓ^(2/3) multiplications.

**Keywords:** Discrete logarithms, random walks, precomputation.

## 1 Introduction

Fully homomorphic encryption is still prohibitively slow, but there are much more efficient schemes achieving more limited forms of homomorphic encryption. We highlight Freeman's variant of the scheme by Boneh, Goh, and Nissim. The Boneh-Goh-Nissim (BGN) scheme can handle adding arbitrary subsets of encrypted data, multiplying the sums, and adding any number of the products. Freeman's variant works in groups typically encountered in pairing-based protocols. The scheme is vastly more efficient than schemes handling unlimited numbers of additions and multiplications. Encryption takes only one exponentiation, as does addition of encrypted messages; multiplication takes a pairing computation.

The limitation to one level of multiplication means that polynomial expressions of degree at most 2 can be evaluated over the encrypted messages, but this is sufficient for a variety of protocols. For example, presented protocols for private information retrieval, elections, and generally universally verifiable computation. There are 395 citations of so far, according to Google Scholar.

The BGN protocol does not have any built-in limit on the number of ciphertexts added, but it does take more time to decrypt as this number grows. The problem is that decryption requires computing a discrete logarithm, where the message is the unknown exponent. If this message is a sum of B products of sums of A input messages from the space {0, . . ., M}, then the final message can be essentially anywhere in the interval [0, (AM)²B]. This means that even if the space for the input messages is limited to bits {0, 1}, the discrete-logarithm computation needs to be able to handle the interval [0, A²B]. For “random” messages the result is almost certainly in a much shorter interval, but most applications need to be able to handle non-random messages.

Boneh, Goh, and Nissim suggested using Pollard's kangaroo method for the discrete-logarithm computation. This method runs in time Θ(l^(1/2)) for an interval of size l. This bottleneck becomes quite troublesome as A and B grow.

For larger message spaces, Hu, Martin, and Sunar in sped up the discrete-logarithm computation at the cost of expanding the ciphertext length and slowing down encryption and operations on encrypted messages. They suggested
representing the initial messages by their residues modulo small coprime numbers d₁, ..., dⱼ with Πdᵢ > (AM)²B, and encrypting these j residues separately. This means that the ciphertexts are j times as long and that each operation on the encrypted messages is replaced by j operations of the same type on the components. The benefit is that each discrete logarithm is limited to [0, (Ad₁)²B], which is a somewhat smaller interval. The original messages are reconstructed using the Chinese remainder theorem.

### Contributions to BGN.
This paper explains (Section 3) how to speed up computations of small discrete logarithms, i.e., discrete logarithms in small intervals. The speedup requires a one-time computation of a small group-specific table. The speedup grows as the table grows; an interesting special case is a table of size Θ(l^(1/3)), speeding up the discrete logarithm to Θ(l^(1/3)) group operations. The space for the table (and the one-time cost for computing the table) is not a problem for the sizes of l used in these applications.

Our experiments (Section 4) show discrete logarithms in an interval of order l taking only 1.93 · l^(1/3) multiplications on average using a table of size l^(1/3). Precomputation of the table used 1.21 · l^(2/3) multiplications. This paper also explains (Section 5) how to compress each table entry below lg l bits with negligible overhead.

This algorithm directly benefits the BGN scheme for any message size M. As an illustration, consider the common binary case M = 1, and assume A = B. The cost of decryption then drops from Θ(A^(3/2)) (superlinear in the number of additions carried out) to just Θ(A), using a table of size Θ(A). The same speedup means that can afford to use fewer moduli, saving both space and time.

### Further applications of discrete logarithms in small intervals.
Many protocols use only degree-1-homomorphic encryption: i.e., addition without any multiplications. The pairing used in the BGN protocol is then unnecessary: one can use a faster elliptic curve that does not support pairings. Decryption still requires a discrete-logarithm computation, this time on the elliptic curve rather than in a multiplicative group. These protocols can also use Paillier’s homomorphic cryptosystem, but elliptic curves provide faster encryption and smaller ciphertexts.

As an example we mention the basic aggregation protocol proposed by Kursawe, Danezis, and Kohlweiss in to enable privacy for smart-meter power-consumption readings. The power company obtains the aggregated consumptions Σcⱼ in the exponent as g^Σcⱼ, and compares this to its own measurement of the total consumption e by checking whether log𝓰(g^Σcⱼ/gᵉ) lies within a tolerance interval. This is another example of a discrete-logarithm computation in a small fixed interval within a large, secure group; we use a small group-specific table to speed up this computation, allowing larger intervals, more aggregation, and better privacy. In cases of sufficiently severe cheating, the discrete logarithm will be too large, causing any discrete-logarithm computation to fail; one recognizes this case by seeing that the computation is running several times longer than expected.

### Applications of discrete logarithms in small groups.
Another interesting category of applications uses “trapdoor discrete-logarithm groups”: groups in which computations of discrete logarithms are feasible with some trapdoor information and hard otherwise. These applications include the Maurer-Yacobi ID-based encryption system in, for example, and the Henry-Henry-Goldberg privacy-preserving protocol in.

Maurer and Yacobi in [24, Section 4] introduced a construction of a trapdoor discrete-logarithm group, with a quadratic gap between the user’s cost and the attacker’s cost. A different construction uses Weil descent with isogenies as trapdoor; see for credits and further discussion of both constructions.

The Maurer-Yacobi construction works as follows. Choose an RSA modulus n = pq, where p−1 and q−1 have many medium-size factors — distinct primes lᵢ chosen small enough that a user knowing the factors of p−1 and q−1 can solve discrete logarithms in each of these subgroups, using Θ(lᵢ^(1/2)) multiplications modulo p or q, but large enough that the ρ−1 method for factoring n, using Θ(lᵢ) multiplications modulo n, is out of reach. The group (Z/nZ)∗ is then a trapdoor discrete-logarithm group. The trapdoor information consists of p, q, and the primes lᵢ. Note that the trapdoor computation here consists of computations of discrete logarithms in small groups, not small intervals inside larger groups; this turns out to make our techniques slightly more efficient.

Henry and Goldberg in presented a fast GPU implementation of the trapdoor computation of discrete logarithms, using Pollard’s rho method. A simple GMP-based implementation of our algorithm on a single core of a low-cost AMD CPU takes an order of magnitude less wall-clock time than the optimized GPU implementation described in, for the same DLP sizes considered in, even though the GPU is more than 30 times faster than the CPU core at modular multiplications. Specifically, for a group of prime order almost exactly 2⁴⁸, our experiments show a discrete-logarithm computation taking just 115729 ≈ 1.77 · 2¹⁶ multiplications on average, using a table of size 65536 = 2¹⁶. Precomputation of the table used 5333245354 ≈ 1.24 · 2³² multiplications.

### Previous work.
Escott, Sager, Selkirk, and Tsapakidis in [9, Section 4.4] showed experimentally that attacking a total of 2, 3, 4, 5 DLPs with the parallel rho method took, respectively, 1.52, 1.90, 2.22, 2.49 times longer than solving just one DLP. The basic idea, which said “has also been suggested by Silverman and Stapleton” in 1997, is to compute log𝓰h₁ with the rho method; compute log𝓰h₂ with the rho method, reusing the distinguished points produced by h₁; compute log𝓰h₃ with the rho method, reusing the distinguished points produced by h₁ and h₂; etc.

Kuhn and Struik in analyzed this method and concluded that solving a batch of L discrete logarithms in a group of prime order l reduces the cost of an average discrete logarithm to Θ(L⁻¹/²l¹/²) multiplications — but only for L < l¹/⁴; see [20, Theorem 1]. Each discrete logarithm here costs at least Θ(l³/⁸); see [20, footnote 5].

Hitchcock, Montague, Carter, and Dawson in [17, Section 3] viewed the computation of many preliminary discrete logarithms log𝓰h₁, log𝓰h₂, . . . as a precomputation for the main computation of log𝓰hₖ, and analyzed some tradeoffs between the main computation time and the precomputation time. Two much more recent papers, independent of each other, have instead emphasized tradeoffs between the main computation time and the space for a table of precomputed distinguished points. The earlier paper, by Lee, Cheon, and Hong, pointed out that these algorithms are tools not just for the cryptanalyst but for the cryptographer, specifically for trapdoor discrete-logarithm computations. The later paper, our paper, pointed out that these algorithms illustrate the gap between the time and space taken by an attack and the difficulty of finding the attack, causing trouble for security definitions in the provable-security literature. Both and clearly break the Θ(l³/⁸)-time-per-discrete-logarithm barrier from.

In this paper we point out that the same idea, suitably adapted, works not only for discrete logarithms in small groups (“rho”) but also for discrete logarithms in small intervals (“kangaroos”). This is critical for BGN-type protocols. We also point out three improvements applicable to both the rho setting and the kangaroo setting: we reduce the number of multiplications by a constant factor by choosing the table entries more carefully; we further reduce the number of multiplications by choosing the iteration function more carefully; and we reduce the space consumed by each table entry. This paper includes several illustrative experiments.

## 2 Review of generic discrete-logarithm algorithms

This section reviews several standard “square-root” methods to compute discrete logarithms in a group of prime order l. Throughout this paper we write the group operation multiplicatively, write g for the standard generator of the group, and write h for the DLP input; our objective is thus to compute log𝓰h, i.e., the unique integer k modulo l such that h = gᵏ.

All of these methods are “generic”: they work for any order-l group, given an oracle for multiplication (and assuming sufficient hash randomness, for the methods using a hash function). “Square-root” means that the algorithms take Θ(l¹/²) multiplications on average over all group elements h.

### Shanks's baby-step-giant-step method.
The baby-step-giant-step method computes ⌈l/W⌉ “giant steps” g⁰, gᵂ, g²ᵂ, g³ᵂ, . . . and then computes a series of W “baby steps” h, hg, hg², . . . , hgᵂ⁻¹. Here W is an algorithm parameter. It is easy to see that there will be a collision gⁱᵂ = hgʲ, revealing log𝓰h = iW − j.

Normally W is chosen as Θ(l¹/²), so that there are O(l¹/²) multiplications in total; more precisely, as (1 + o(1))l¹/² so that there are ≤ (2 + o(1))l¹/² multiplications in total. Interleaving baby steps with giant steps, as suggested by Pollard in [29, page 439, top], obtains a collision after (4/3 + o(1))l¹/² multiplications on average. We have recently introduced a “two grumpy giants and a baby” variant that reduces the constant 4/3; see.

The standard criticism of these methods is that they use a large amount of memory, around l¹/² group elements. One can reduce the giant-step storage to, e.g., Θ(l¹/³) group elements by taking W as Θ(l²/³), but this also increases the average number of baby steps to Θ(l²/³). This criticism is addressed by the rho and kangaroo methods discussed below, which drastically reduce space usage while still using just Θ(l¹/²) multiplications.

### Pollard's rho method.
Pollard’s original rho method [28, Section 1] computes a pseudorandom walk 1, F(1), F(F(1)), . . . . Here F(u) is defined as gu or u² or hu, depending on whether a hash of u is 0 or 1 or 2. Each iterate Fᵐ(1) then has the form gˣhʸ for some easily computed pair (x, y) ∈ (Z/lZ)², and any collision gˣhʸ = gˣ'hʸ' with (x, y) ≠ (x', y') immediately reveals log𝓰h. One expects a sufficiently random-looking walk on l group elements to collide with itself within O(l¹/²) steps. There are several standard methods to find the collision with negligible memory consumption.

Van Oorschot and Wiener in proposed running many walks in parallel, starting from different points gˣhʸ and stopping each walk when it reaches a “distinguished point”. Here a fraction 1/W of the points are defined (through another hash function) as “distinguished”, where W is an algorithm parameter; each walk reaches W points on average. One checks for collisions only among the occasional distinguished points, not among all of the group elements produced. The critical observation is that if two walks reach the same group element then they will eventually reach the same distinguished point — or will enter cycles, but cycles have negligible chance of appearing if W is below the scale of l¹/².

There are many other reasonable choices of F. One popular choice — when there are many walks as in, not when there is a single walk as in [28, Section 1] — is a “base-g r-adding walk”: this means that the hash function has r different values, and F(u) is defined as s₁u or s₂u or . . . or sᵣu respectively, where s₁, s₂, . . . , sᵣ are precomputed as random powers of g. One then starts each walk at a different power hⁱ. This approach has several minor advantages (for example, x is constant in each walk and need not be updated) and the major advantage of simulating a random walk quite well as r increases. See, e.g.,,, and for further discussion of the impact of r. The bottom line is that this method finds a discrete logarithm within (√π/2 + o(1))l¹/² multiplications on average.

The terminology “r-adding walk” is standard in the literature but the terminology “base-g r-adding walk” is not. We use this terminology to distinguish a base-g r-adding walk from a “base-(g, h) r-adding walk”, in which s₁, s₂, . . . , sᵣ are precomputed as products of random powers of g and h. This distinction is critical in Section 3.

### Pollard's kangaroo method.
An advantage of baby-step-giant-step, already exploited by Shanks in the paper introducing the method, is that it immediately generalizes from computing discrete logarithms in any group of prime order l to computing discrete logarithms in any interval of length l inside any group of prime order p > l. The rho method uses Θ(p¹/²) group operations, often far beyond Θ(l¹/²) group operations.

Pollard in [28, Section 3] introduced a “kangaroo” method that combines the advantages of the baby-step-giant-step method and the rho method: it takes only Θ(l¹/²) group operations to compute discrete logarithms in an interval of length l, while still using negligible memory. This method:
- chooses a base-g r-adding iteration function whose steps have average exponents Θ(l¹/²), instead of exponents chosen uniformly modulo l;
- runs a walk starting from gʸ (the “tame kangaroo”), where y is at the right end of the interval;
- records the Wth step in this walk (the “trap”), where W is Θ(l¹/²); and
- runs a walk (the “wild kangaroo”) starting from h, checking at each step whether this walk has fallen into the trap.

van Oorschot and Wiener in proposed a parallel kangaroo method in which tame kangaroos start from gʸ for many values of y, all close to the middle of the interval, and a similar number of wild kangaroos start from hgʸ' for many small values of y'. Collisions are detected by distinguished points as in the parallel rho method, but the distinguished-point property is chosen to have probability considerably higher than 1/W; walks continue past distinguished points. The walks are adjusted to avoid collisions between tame kangaroos and to avoid collisions between wild kangaroos. Several subsequent papers have proposed refinements of the kangaroo method, obtaining constant-factor speedups.

### The Nechaev–Shoup bound.
Shoup proved in that all generic discrete-logarithm algorithms have success probability O(m²/l) after m multiplications. (The same bound had been proven by Nechaev in for a more limited class of algorithms, which one might call “representation oblivious” generic discrete-logarithm algorithms.) All generic discrete-logarithm algorithms therefore need Ω(l¹/²) multiplications on average; i.e., the usual square-root discrete-logarithm algorithms are optimal up to a constant factor. A closer look shows that the lower bound is (2√2/3 + o(1))l¹/², so both the baby-step-giant-step method and the rho method are within a factor 2 + o(1) of optimal.

There are much faster discrete-logarithm algorithms (e.g., index-calculus algorithms) for specific classes of groups. However, the conventional wisdom is that these square-root algorithms are the fastest discrete-logarithm algorithms for “secure” groups: a sensibly chosen elliptic-curve group, for example, or the order-l subgroup of F*ₚ for sufficiently large p.

In the rest of this paper we discuss algorithms that improve upon these square-root algorithms by a non-constant factor. Evidently these improved algorithms do not fit Shoup’s model of “generic” algorithms — but these improved algorithms do apply to “secure” groups. The algorithms deviate from the “generic” model by requiring an extra input, a small table that depends on the group but not on the particular discrete logarithm being computed. The table is set up by a generic algorithm, and if one views the setup and use of the table as a single unified algorithm then Shoup’s bound applies to that algorithm; but if the table is set up once and used enough times to amortize the setup costs then each use of the table evades Shoup’s bound.

... (The document continues for many more pages, and the full conversion would exceed the reasonable length of a single response. The provided text covers the first 6 pages and demonstrates the conversion to Markdown format, including headings, lists, mathematical notation, and citation styling.)

## 3 使用小表加速通用离散对数算法

本节介绍如何使用一个小表来加速 Pollard 的 rho 和 kangaroo 方法。这个表依赖于群和基点 g，但不依赖于目标 h。对于区间，该表依赖于区间的长度，但不依赖于区间的位置：将 h 除以 gª 可以将区间 {A, A + 1, ..., A + l − 1} 中的离散对数问题简化为区间 {0, 1, ..., l − 1} 中的离散对数问题，从而消除了 A 的影响。

加速因子随着表大小 T 的平方根增长。随着 T 的增长，计算离散对数所需的平均乘法次数远低于上一节中使用的 ≈ℓ¹/² 次。

建立表的成本大于 ℓ¹/²，也随着 T 的平方根增长。然而，这个成本可以分摊到所有使用同一张表处理的目标 h 上。将建表成本 (lT)¹/² 与离散对数计算成本 (l/T)¹/² 进行比较，可以看出当处理的目标数量超过 T 时，建表成本变得可以忽略不计。

该算法的主要参数是表大小 T 和步长 W。合理的参数选择将满足 W ≈ α(l/T)¹/²，其中 α 是一个下面讨论的小常数。辅助参数是用于建表的各种决策；这些决策将在下面进行分析。

### 基本算法
为简单起见，我们首先描述一个使用小表加速 rho 方法的“基本算法”。然后我们描述对该基本算法的加速，最后是一个使用小表加速 kangaroo 方法的变体。

**基本算法。** 要建立表，只需从随机选择的 y 值的 gʸ 开始进行一些随机游走。表项是由这些游走产生的不同特征点及其离散对数。

这里的关键是，游走中使用的迭代函数必须独立于 h。标准的 base-g r-adding 游走满足此条件，为简单起见，我们关注 base-g r-adding 游走的情况，尽管我们建议实现者也尝试使用一些平方的“混合游走”。有时游走会发生碰撞（当参数选择合理时，这种情况会频繁发生），因此建立表需要多于 T 次游走；下文将对此效应进行量化。

要使用此表找到 h 的离散对数，从随机选择的 x 值的 hˣgᵃ 开始游走，产生各种特征点 hˣgᵃ'，就像通常的 rho 方法一样。检查这两个新的特征点是否碰撞，同时也要检查其中一个新特征点是否与预计算表中的一个特征点碰撞。任何这样的碰撞都会立即揭示 log𝓰h。

实际上，该表作为算法自然累积的特征点列表的免费基础。如果与 h 相关的游走次数远小于 T（当参数选择合理时会发生这种情况），那么可以合理地跳过对两个新特征点碰撞的检查；该算法几乎总是通过与预计算表中的特征点碰撞而成功。

**特殊情况。** T = 0 的极端情况是使用 base-g r-adding 游走（或更一般地，使用任何与 h 无关的迭代函数）的常规 rho 方法。然而，我们主要关注的是由较大 T 值提供的加速。

我们还提请注意指数为 1 的 r = 1 的极端情况，即简单地从 u 步进到 gu。在这种情况下，主要的“rho”计算包括平均进行 W 次“小步” hx, hg, hg², ...，然后在一个表中查找得到的特征点。这个案例的有趣之处在于它与 baby-step-giant-step 方法的明显相似性，但优点是仅在 W 次小步后才进行表访问；而常规的 baby-step-giant-step 方法则在每一步小步后都检查表。这种情况的缺点是游走高度非随机，需要 O(l) 步才能与另一条这样的游走碰撞；较大的 r 值则能在 O(l¹/²) 步内产生碰撞。

回想一下第 1 节中解决多个离散对数问题的经典算法：对每个 k，依次计算 log𝓰hₖ，并重用由 h₁, ..., hₖ₋₁ 产生的特征点。此计算的 log𝓰hₖ 部分显然符合这里讨论的算法，其中 T 被隐式定义为由 h₁, ..., hₖ₋₁ 产生的特征点数量。然而，我们强调，这是对 T 的一种特殊选择，并且该参数曲线不符合上面提到的关系。

### 优化步长
假设 W ≈ α(l/T)¹/²，并考虑单次游走遇到表中 T 个特征点之一从而解决 DLP 的机会。T 个表项是从大概覆盖了约 W 个点的游走中获得的，总共覆盖了 TW 个点。新的游走也覆盖了约 W 个点，因此有 TW² ≈ α²l 个碰撞机会。如果这些碰撞机会是独立的，那么避开所有这些碰撞的几率将是 (1 − 1/l)^(α²l) ≈ exp(−α²)。

这个启发式分析表明，单次游走成功的概率为，例如，当 α = 1/4 时，概率为 1 − exp(−1/16) ≈ 6%；当 α = 1/2 时，概率为 1 − exp(−1/4) ≈ 22%；当 α = 1 时，概率为 1 − exp(−1) ≈ 63%；当 α = 2 时，概率为 1 − exp(−4) ≈ 98%。

同样的分析还表明，预计算的最后阶段，即找到表中的第 T 个点，将需要尝试 exp(1/16) ≈ 1.06 次长度为 W 的游走（对于 α = 1/4），或 exp(1/4) ≈ 1.28 次长度为 W 的游走（对于 α = 1/2），或 exp(1) ≈ 2.72 次长度为 W 的游走（对于 α = 1），或 exp(4) ≈ 54.6 次长度为 W 的游走（对于 α = 2）。

采用非常小的 α 的明显优势在于可以合理地并行执行多次游走。例如，取 α = 1/8 平均需要 64 次游走，如果并行执行 4 次游走，则最多浪费 3 次游走。并行化最常见的理由是它允许利用多核，减少延迟。即使延迟不是问题，并行化也很有帮助：例如，它允许在仿射椭圆曲线计算中合并求逆（Montgomery 技巧），并且通常允许有效利用单核中的向量单元。解决许多独立的离散对数问题也能产生同样的好处，但这需要应用程序同时准备好许多独立的问题。

采用非常小的 α 的明显缺点是，每次游走的成功概率随 α 平方下降，而步长仅随 α 线性下降。换句话说，将一个小的 α 减半会使每一步的效率减半，使预期的计算步数加倍。有时，并行化的增加（现在游走次数是原来的四倍）会超过这个缺点，但显然，α 能取多小是有限度的。

显然，α 能取多大也是有限度的。将 α 增加到 1 以上并不会使每一步的效率提高一倍：α = 1 的游走已经有 63% 的成功机会；α = 2 的游走成功机会为 98%，但成本是两倍。

我们实际上建议通过实验来优化 α（而不是将其限制为 2 的幂），而不是相信上面显示的启发式分析的精确细节。启发式分析的一个小问题是，新的游走有时只走 W/2 步，其碰撞概率远低于指示的概率，有时则走 2W 步；一次游走的成功概率与一次长度为 W 的游走的成功概率不同。一个更大的问题是，TW 只是对表覆盖范围的一个粗略近似。

### 选择最有用的特征点
我们建议生成比 T 更多的特征点，比如 2T、10T 或 1000T，然后保留 T 个最有用的特征点，而不是随机生成 T 个特征点。

“最有用”的自然定义是“拥有最多的祖先”。根据定义，一个特征点的祖先是所有能走到该点的群元素；一个均匀随机的群元素走到这个特征点的机会恰好是其祖先数量除以 l。

不幸的是，如果不花时间遍历所有 l 个群元素，就无法知道一个特征点的祖先数量。幸运的是，我们有一个对此数量的统计估计：被多次游走发现的特征点很可能比被较少次游走发现的特征点更有用。对于只被少数几次游走，尤其只被一次游走发现的特征点，这个估计是不可靠的。

### 迭代函数的选择与惩罚
选择与目标 h 无关的迭代函数会带来一定的性能损失。为了理解这种惩罚，考虑主计算通过一次游走成功的概率，即其产生的特征点出现在表中的概率。大约有 ≈l/W 个特征点，表中包含其中的 T 个，所以明显的初步近似是主计算成功的概率为 TW/l。如果表是由与主计算中使用的游走无关的随机游走生成的，那么这个近似是相当合理的。如果表是由主计算中使用的相同游走生成的，那么独立性假设不再适用，并且这个近似结果被证明是严重低估了。

实际上， 中的表生成过程是从特征点集合中均匀随机地选择表项。而、 和 中的表生成过程则是从随机群元素开始游走到特征点；这在表的覆盖范围内产生了一个高度非均匀的分布，偏向于更有用的特征点。我们更进一步，通过从一个更大的特征点池中仔细选择，使表项更加偏向有用。

### 适应小区间的方法
我们现在解释一小组调整，使上述基本算法适应于计算长度为 l 的区间内的离散对数问题。这些调整与上述的改进（例如选择最有用的特征点）可以轻易地结合起来。

与标准的 kangaroo 方法一样，选择步长 s₁, s₂, ..., sᵣ 为 g 的幂，其中指数平均为 βl/W。我们建议对常数 β 进行数值优化。

从区间内随机选择的 y 值的 gʸ 开始游走。与基本算法一样，当每次游走到达一个特征点时停止，并建立一个包含这些特征点离散对数的表。

要找到 h 的离散对数，从一个随机小数 y' 的 hgʸ' 开始游走；在第一个特征点处停止；并检查产生的特征点是否在表中。在我们的实验中，我们将“小”定义为“小于 l/256”，但从 h、hg、hg² 等开始第一次游走也是合理的。

## 4 实验

本节报告了使用第 3 节中描述的算法进行的几次实验，包括针对小群和较大群内小区间的实验。为了便于验证，我们在 http://cr.yp.to/dlog/cuberoot.html 上发布了我们用于典型小区间实验的软件。

### 案例研究：小群实验
我们从针对 [15, 表 2, 第一行] 中描述的模 pq 离散对数问题的几次实验开始。在这里，p 和 q 是“768 位素数”，其生成方式使得 p-1 和 q-1 是“2⁴⁸-平滑”的；大概这意味着 (p-1)/2 是 16 个略低于 2⁴⁸ 的素数的乘积，(q-1)/2 也是如此。原始的离散对数问题随后分解为 16 个独立的模 p 的 48 位 DLP 和 16 个独立的模 q 的 48 位 DLP。

 的报告指出，一个 448-ALU NVIDIA Tesla M2050 显卡平均需要 23 秒来完成这 32 个离散对数计算，即每个 48 位离散对数计算需要 0.72 秒。

我们生成了一个整数 p = 1 + 2l₁l₂...l₁₆，其中 l₁, l₂, ..., l₁₆ 是介于 2⁴⁸ − 2²⁰ 和 2⁴⁸ 之间的随机素数。我们重复这个过程直到 p 是素数，然后取 l = l₁。这个 l 结果是 2⁴⁸ − 313487。

我们选择了 T = 64 和 W = 1048576；这里 α = 1/2，即 W ≈ (1/2)(l/T)¹/²。预计算 T 个表项总共使用了 80289876 ≈ 1.20TW ≈ 0.60(lT)¹/² 次乘法。

然后我们进行了 1024 次离散对数实验。这些实验总共使用了 1040325443 ≈ 0.97 · 1024W 次乘法，成功了 192 次，平均每次成功的离散对数计算使用 5418361 ≈ 2.58(l/T)¹/² 次乘法。

**更有用的特征点。** 然后我们改变了预计算，保留 T = 64 和 W = 1048576，但从一个包含 N = 128 个特征点的池中选择 T 个表项作为最有用的 64 个。这将预计算成本增加到 167040079 ≈ 1.24NW ≈ 1.24(lT)¹/² 次乘法。我们运行了 4096 次新的离散对数实验，成功了 1060 次，平均每次成功的离散对数计算使用 3755123 ≈ 1.79(l/T)¹/² 次乘法。

**T¹/² 缩放。** 然后我们将 W 减少到 262144，将 T 增加到 1024，N 增加到 2048。这使预计算成本增加到 626755730 ≈ 1.17NW ≈ 1.17(lT)¹/² 次乘法。我们运行了 8192 次新实验，成功了 2265 次，平均每次成功的离散对数计算仅使用 937520 ≈ 1.79(l/T)¹/² 次乘法。正如预测的那样，T 增加 16 倍，步数减少了 4 倍。

我们还检查了这些计算的运行速度超过每秒 140 万次乘法，即每次离散对数计算不到 0.67 秒——比 在整个 GPU 上所需的时间还短。

**优化 α。** 我们随后进行了一系列实验，W = 524288，同时改变 T 和 N/T，如表 4.1 所示。该表显示，α 的最优选择取决于比率 N/T，但 α 在最优值附近的较大变化对性能影响相对较小。性能更多地受到增加的 N/T，即额外预计算的影响。

**小区间实验。** 从相同的软件开始，我们随后做了以下调整，以计算一个更大素数阶群内的小区间内的离散对数。结果见表 4.2。将表 4.2 与表 4.1 进行比较，可以看出这种在长度为 l 的区间内计算离散对数的方法——对于相同的表大小和基本相同的预计算量——比在阶为 l 的群中计算离散对数所需的乘法次数仅略多。

## 5 空间优化

第 3 节中描述的每个表项由一个群元素（至少 lg l 位）和一个离散对数（也是 lg l 位）组成，总共至少 2Tlg l 位。本节解释了几种将表压缩到更小位数的方法。

### 每个特征点的无损压缩
有几种标准技术可以可逆地压缩常用群的元素。例如，“Curve25519”椭圆曲线群的非零元素是满足 y² = x³ + 486662x² + x 的点对 (x, y) ∈ F_q × F_q；这里 q = 2²⁵⁵ − 19 且 l ≈ 2²⁵²。这个点对可以被简单地压缩为 x 和 y 的一位，总共 256 ≈ lg l 位。

### 用哈希替换每个特征点
为了做得更好，我们简单地抑制一些额外的位：我们将每个特征点哈希到更少的位数，并存储哈希值而不是特征点本身。这会产生误报的风险，但误报的成本仅仅是检查一个错误的 log𝓰h 猜测的成本。

### 压缩排序的哈希序列
众所周知，一个包含 n 个 d 位整数的排序序列所包含的信息远少于 nd 位。“增量压缩”利用了这一点，通过计算连续整数之间的差并对差值使用可变长度编码。

### 压缩每个离散对数
我们最后考虑两种压缩表中离散对数的机制。第一种机制是在进行中的 ECC2K-130 计算中引入的；见。第二种机制似乎是新的。

第一种机制如下。与其选择一个随机的 y 并从 gʸ 开始游走，不如选择一个由短种子确定的伪随机 y。将种子存储为所产生的特征点离散对数的代理。

第二种机制是简单地抑制离散对数的大部分位。重建这些位就变成了一个在更小区间内的离散对数问题；用相同的算法递归地解决这些重建问题，使用更小的表和更少的乘法。

请注意，这第二种机制依赖于能够快速计算小区间内的离散对数，即使最初的目标是在小群中计算离散对数。

## 参考文献
 (无编辑), 2nd ACM conference on computer and communication security, Fairfax, Virginia, November 1994, Association for Computing Machinery, 1994. See.
 Mikhail J. Atallah, Nicholas J. Hopper (editors), Privacy enhancing technologies, 10th interational symposium, PETS 2010, Berlin, Germany, July 21-23, 2010, proceedings, Lecture Notes in Computer Science, 6205, Springer, 2010. ISBN 978-3-642-14526-1. See.
... (此处省略了完整的参考文献列表，其格式与原文一致) ...