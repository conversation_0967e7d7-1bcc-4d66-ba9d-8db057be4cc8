.\Release\test_bernstein_search.exe -t bernstein_lange_table_2_22.bin -p 03633cbe3ec02b9401c5effa144c5b4d22f87940259634858fc7e59b1c09937852 -k 000000000000000000000000000000033e7665705359f04f28b88cf897c603c9 -r 200000000000000000000000000000000 -s 1ffffffffffffffffffffffffffffffff -i 100 -s 1ffffffffffffffffffffffffffffffff -i 1;d286aedf-691c-4093-a4e2-73577775d274
00 -s 1ffffffffffffffffffffffffffffffff -i 1;d286aedf-691c-4093-a4e2-73577775d274🧪 Bernstein-Lange完整搜索测试
==========================================
📋 配置参数:
  预计算表: bernstein_lange_table_2_22.bin
  目标公钥: 03633cbe3ec02b9401c5effa144c5b4d22f87940259634858fc7e59b1c09937852
  搜索起始: 0x200000000000000000000000000000000
  区间大小: 0x1ffffffffffffffffffffffffffffffff
  最大迭代: 1

📋 步骤1: 初始化SECP256K1...
✅ SECP256K1初始化完成

📋 步骤2: 创建BernsteinKangaroo搜索器...
🔧 使用与Kangaroo.cpp相同的跳跃表生成逻辑...
  跳跃位数: 66
✅ 跳跃点初始化完成 (32 个)
  使用与Kangaroo::CreateJumpTable()完全相同的算法

📋 步骤3: 加载预计算表...
🔧 加载Bernstein-Lange预计算表...
📂 加载Bernstein-Lange预计算表: bernstein_lange_table_2_22.bin
📊 表参数信息:
  表大小 T: 4194304
  α参数: 0.786     
  步长 W: 4        
  哈希位数: 22     
  区分点掩码: 0x3  
📥 读取表数据...   
  已读取: 100000 / 4194304 (2%)
  已读取: 200000 / 4194304 (4%)
  已读取: 300000 / 4194304 (7%)
  已读取: 400000 / 4194304 (9%)
  已读取: 500000 / 4194304 (11%)
  已读取: 600000 / 4194304 (14%)
  已读取: 700000 / 4194304 (16%)
  已读取: 800000 / 4194304 (19%)
  已读取: 900000 / 4194304 (21%)
  已读取: 1000000 / 4194304 (23%)
  已读取: 1100000 / 4194304 (26%)
  已读取: 1200000 / 4194304 (28%)
  已读取: 1300000 / 4194304 (30%)
  已读取: 1400000 / 4194304 (33%)
  已读取: 1500000 / 4194304 (35%)
  已读取: 1600000 / 4194304 (38%)
  已读取: 1700000 / 4194304 (40%)
  已读取: 1800000 / 4194304 (42%)
  已读取: 1900000 / 4194304 (45%)
  已读取: 2000000 / 4194304 (47%)
  已读取: 2100000 / 4194304 (50%)
  已读取: 2200000 / 4194304 (52%)
  已读取: 2300000 / 4194304 (54%)
  已读取: 2400000 / 4194304 (57%)
  已读取: 2500000 / 4194304 (59%)
  已读取: 2600000 / 4194304 (61%)
  已读取: 2700000 / 4194304 (64%)
  已读取: 2800000 / 4194304 (66%)
  已读取: 2900000 / 4194304 (69%)
  已读取: 3000000 / 4194304 (71%)
  已读取: 3100000 / 4194304 (73%)
  已读取: 3200000 / 4194304 (76%)
  已读取: 3300000 / 4194304 (78%)
  已读取: 3400000 / 4194304 (81%)
  已读取: 3500000 / 4194304 (83%)
  已读取: 3600000 / 4194304 (85%)
  已读取: 3700000 / 4194304 (88%)
  已读取: 3800000 / 4194304 (90%)
  已读取: 3900000 / 4194304 (92%)
  已读取: 4000000 / 4194304 (95%)
  已读取: 4100000 / 4194304 (97%)
✅ 预计算表加载完成
📊 预计算表统计信息:
  总条目数: 4194304
  有效条目数: 4194304
  填充率: 100%
  唯一哈希数: 2164137
  哈希碰撞数: 2030167
  平均每哈希条目数: 1.9381
✅ 预计算表加载成功
  步长W: 4
  区分点掩码: 0x3
  哈希位数: 22

📋 步骤4: 设置测试用例...
  目标公钥x: 0x633CBE3EC02B9401C5EFFA144C5B4D22F87940259634858FC7E59B1C09937852
  目标公钥y: 0xB078A17CC1558A9A4FA0B406F194C9A2B71D9A61424B533CEEFE27408B3191E3
  搜索区间: [0x200000000000000000000000000000000, 0x3FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF]
✅ 使用Int类型计算区间长度，支持大数运算
🎯 设置搜索区间:
  起始: 0x200000000000000000000000000000000
  结束: 0x3FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
  长度: 0x200000000000000000000000000000000
🎯 设置目标公钥:
  x: 0x633CBE3EC02B9401C5EFFA144C5B4D22F87940259634858FC7E59B1C09937852
  y: 0xB078A17CC1558A9A4FA0B406F194C9A2B71D9A61424B533CEEFE27408B3191E3
🔍 设置已知私钥用于碰撞分析: 0x33E7665705359F04F28B88CF897C603C9
  已设置已知私钥用于碰撞分析

📋 步骤5: 执行Bernstein-Lange搜索...

🚀 开始Bernstein-Lange搜索...
  最大迭代次数: 1
  估算参数: L=0x200000000000000000000000000000000, T=4194304, ratio=0, steps=100
  每次搜索最大步数: 100
  随机扰动范围: [1, 100], 生成: 83

🔍 搜索尝试 #1
  随机扰动 z: 0x53
  🎯 检测到碰撞 #1! 步数: 2
    区分点哈希: 0x2ea7bc
    表中数值: 0x47e501

[DEBUG] 🎯 碰撞 #1 详细分析:
[DEBUG] 区分点哈希: 0x2ea7bc
  🧮 执行最终计算...
[DEBUG] 表中存储的 (y+d) 值: 0x47e501 (4711681)
[DEBUG] 累积标量 (z+d_search): 0x55 (85)
[DEBUG] 转换为有符号: table_log=4711681, accumulated=85
[DEBUG] k_raw (有符号): 4711596
[DEBUG] k_raw (十六进制): 0x47e4ac
    表中数值: 4711681
    累积标量: 85
    原始k值: 4711596
[DEBUG] 区间长度 L: 0x200000000000000000000000000000000
[DEBUG] 区间起点 A: 0x200000000000000000000000000000000
[DEBUG] k_raw (Int): 0x47E4AC
[DEBUG] 🎯 关键结果 k_in_range (Int): 0x47E4AC
[DEBUG] 最终计算: A + k_in_range
[DEBUG] 最终私钥: 0x20000000000000000000000000047E4AC
    区间内k值: 0x47E4AC
    最终私钥: 0x20000000000000000000000000047E4AC
  📊 碰撞分析 #1:
    计算私钥: 0x20000000000000000000000000047E4AC
    真实私钥: 0x33E7665705359F04F28B88CF897C603C9
  🔍 验证私钥...
    ❌ 私钥验证失败，公钥不匹配
  ⚠️ 误报，继续搜索...
  🎯 检测到碰撞 #2! 步数: 8
    区分点哈希: 0x122f3b
    表中数值: 0x47e501

[DEBUG] 🎯 碰撞 #2 详细分析:
[DEBUG] 区分点哈希: 0x122f3b
  🧮 执行最终计算...
[DEBUG] 表中存储的 (y+d) 值: 0x47e501 (4711681)
[DEBUG] 累积标量 (z+d_search): 0x5b (91)
[DEBUG] 转换为有符号: table_log=4711681, accumulated=91
[DEBUG] k_raw (有符号): 4711590
[DEBUG] k_raw (十六进制): 0x47e4a6
    表中数值: 4711681
    累积标量: 91
    原始k值: 4711590
[DEBUG] 区间长度 L: 0x200000000000000000000000000000000
[DEBUG] 区间起点 A: 0x200000000000000000000000000000000
[DEBUG] k_raw (Int): 0x47E4A6
[DEBUG] 🎯 关键结果 k_in_range (Int): 0x47E4A6
[DEBUG] 最终计算: A + k_in_range
[DEBUG] 最终私钥: 0x20000000000000000000000000047E4A6
    区间内k值: 0x47E4A6
    最终私钥: 0x20000000000000000000000000047E4A6
  📊 碰撞分析 #2:
    计算私钥: 0x20000000000000000000000000047E4A6
    真实私钥: 0x33E7665705359F04F28B88CF897C603C9
  🔍 验证私钥...
    ❌ 私钥验证失败，公钥不匹配
  ⚠️ 误报，继续搜索...
  🎯 检测到碰撞 #3! 步数: 9
    区分点哈希: 0xad699
    表中数值: 0x31c5ed

[DEBUG] 🎯 碰撞 #3 详细分析:
[DEBUG] 区分点哈希: 0xad699
  🧮 执行最终计算...
[DEBUG] 表中存储的 (y+d) 值: 0x31c5ed (3261933)
[DEBUG] 累积标量 (z+d_search): 0x5c (92)
[DEBUG] 转换为有符号: table_log=3261933, accumulated=92
[DEBUG] k_raw (有符号): 3261841
[DEBUG] k_raw (十六进制): 0x31c591
    表中数值: 3261933
    累积标量: 92
    原始k值: 3261841
[DEBUG] 区间长度 L: 0x200000000000000000000000000000000
[DEBUG] 区间起点 A: 0x200000000000000000000000000000000
[DEBUG] k_raw (Int): 0x31C591
[DEBUG] 🎯 关键结果 k_in_range (Int): 0x31C591
[DEBUG] 最终计算: A + k_in_range
[DEBUG] 最终私钥: 0x20000000000000000000000000031C591
    区间内k值: 0x31C591
    最终私钥: 0x20000000000000000000000000031C591
  📊 碰撞分析 #3:
    计算私钥: 0x20000000000000000000000000031C591
    真实私钥: 0x33E7665705359F04F28B88CF897C603C9
  🔍 验证私钥...
    ❌ 私钥验证失败，公钥不匹配
  ⚠️ 误报，继续搜索...
  🎯 检测到碰撞 #4! 步数: 10
    区分点哈希: 0x10d09e
    表中数值: 0x1ba6d9

[DEBUG] 🎯 碰撞 #4 详细分析:
[DEBUG] 区分点哈希: 0x10d09e
  🧮 执行最终计算...
[DEBUG] 表中存储的 (y+d) 值: 0x1ba6d9 (1812185)
[DEBUG] 累积标量 (z+d_search): 0x5d (93)
[DEBUG] 转换为有符号: table_log=1812185, accumulated=93
[DEBUG] k_raw (有符号): 1812092
[DEBUG] k_raw (十六进制): 0x1ba67c
    表中数值: 1812185
    累积标量: 93
    原始k值: 1812092
[DEBUG] 区间长度 L: 0x200000000000000000000000000000000
[DEBUG] 区间起点 A: 0x200000000000000000000000000000000
[DEBUG] k_raw (Int): 0x1BA67C
[DEBUG] 🎯 关键结果 k_in_range (Int): 0x1BA67C
[DEBUG] 最终计算: A + k_in_range
[DEBUG] 最终私钥: 0x2000000000000000000000000001BA67C
    区间内k值: 0x1BA67C
    最终私钥: 0x2000000000000000000000000001BA67C
  📊 碰撞分析 #4:
    计算私钥: 0x2000000000000000000000000001BA67C
    真实私钥: 0x33E7665705359F04F28B88CF897C603C9
  🔍 验证私钥...
    ❌ 私钥验证失败，公钥不匹配
  ⚠️ 误报，继续搜索...
  🎯 检测到碰撞 #5! 步数: 26
    区分点哈希: 0x222a3b
    表中数值: 0x31c5ed

[DEBUG] 🎯 碰撞 #5 详细分析:
[DEBUG] 区分点哈希: 0x222a3b
  🧮 执行最终计算...
[DEBUG] 表中存储的 (y+d) 值: 0x31c5ed (3261933)
[DEBUG] 累积标量 (z+d_search): 0x6d (109)
[DEBUG] 转换为有符号: table_log=3261933, accumulated=109
[DEBUG] k_raw (有符号): 3261824
[DEBUG] k_raw (十六进制): 0x31c580
    表中数值: 3261933
    累积标量: 109
    原始k值: 3261824
[DEBUG] 区间长度 L: 0x200000000000000000000000000000000
[DEBUG] 区间起点 A: 0x200000000000000000000000000000000
[DEBUG] k_raw (Int): 0x31C580
[DEBUG] 🎯 关键结果 k_in_range (Int): 0x31C580
[DEBUG] 最终计算: A + k_in_range
[DEBUG] 最终私钥: 0x20000000000000000000000000031C580
    区间内k值: 0x31C580
    最终私钥: 0x20000000000000000000000000031C580
  📊 碰撞分析 #5:
    计算私钥: 0x20000000000000000000000000031C580
    真实私钥: 0x33E7665705359F04F28B88CF897C603C9
  🔍 验证私钥...
    ❌ 私钥验证失败，公钥不匹配
  ⚠️ 误报，继续搜索...

[DEBUG] 🎯 碰撞 #6 详细分析:
[DEBUG] 区分点哈希: 0x33935d
[DEBUG] 表中存储的 (y+d) 值: 0x47e501 (4711681)
[DEBUG] 累积标量 (z+d_search): 0x71 (113)
[DEBUG] 转换为有符号: table_log=4711681, accumulated=113
[DEBUG] k_raw (有符号): 4711568
[DEBUG] k_raw (十六进制): 0x47e490
[DEBUG] 区间长度 L: 0x200000000000000000000000000000000
[DEBUG] 区间起点 A: 0x200000000000000000000000000000000
[DEBUG] k_raw (Int): 0x47E490
[DEBUG] 🎯 关键结果 k_in_range (Int): 0x47E490
[DEBUG] 最终计算: A + k_in_range
[DEBUG] 最终私钥: 0x20000000000000000000000000047E490
  📊 碰撞分析 #6:
    计算私钥: 0x20000000000000000000000000047E490
    真实私钥: 0x33E7665705359F04F28B88CF897C603C9

[DEBUG] 🎯 碰撞 #7 详细分析:
[DEBUG] 区分点哈希: 0x1927a3
[DEBUG] 表中存储的 (y+d) 值: 0x1ba6d9 (1812185)
[DEBUG] 累积标量 (z+d_search): 0x7e (126)
[DEBUG] 转换为有符号: table_log=1812185, accumulated=126
[DEBUG] k_raw (有符号): 1812059
[DEBUG] k_raw (十六进制): 0x1ba65b
[DEBUG] 区间长度 L: 0x200000000000000000000000000000000
[DEBUG] 区间起点 A: 0x200000000000000000000000000000000
[DEBUG] k_raw (Int): 0x1BA65B
[DEBUG] 🎯 关键结果 k_in_range (Int): 0x1BA65B
[DEBUG] 最终计算: A + k_in_range
[DEBUG] 最终私钥: 0x2000000000000000000000000001BA65B
  📊 碰撞分析 #7:
    计算私钥: 0x2000000000000000000000000001BA65B
    真实私钥: 0x33E7665705359F04F28B88CF897C603C9

[DEBUG] 🎯 碰撞 #8 详细分析:
[DEBUG] 区分点哈希: 0x10364a
[DEBUG] 表中存储的 (y+d) 值: 0x587c5 (362437)
[DEBUG] 累积标量 (z+d_search): 0x7f (127)
[DEBUG] 转换为有符号: table_log=362437, accumulated=127
[DEBUG] k_raw (有符号): 362310
[DEBUG] k_raw (十六进制): 0x58746
[DEBUG] 区间长度 L: 0x200000000000000000000000000000000
[DEBUG] 区间起点 A: 0x200000000000000000000000000000000
[DEBUG] k_raw (Int): 0x58746
[DEBUG] 🎯 关键结果 k_in_range (Int): 0x58746
[DEBUG] 最终计算: A + k_in_range
[DEBUG] 最终私钥: 0x200000000000000000000000000058746
  📊 碰撞分析 #8:
    计算私钥: 0x200000000000000000000000000058746
    真实私钥: 0x33E7665705359F04F28B88CF897C603C9

[DEBUG] 🎯 碰撞 #9 详细分析:
[DEBUG] 区分点哈希: 0x2f339a
[DEBUG] 表中存储的 (y+d) 值: 0x587c5 (362437)
[DEBUG] 累积标量 (z+d_search): 0x81 (129)
[DEBUG] 转换为有符号: table_log=362437, accumulated=129
[DEBUG] k_raw (有符号): 362308
[DEBUG] k_raw (十六进制): 0x58744
[DEBUG] 区间长度 L: 0x200000000000000000000000000000000
[DEBUG] 区间起点 A: 0x200000000000000000000000000000000
[DEBUG] k_raw (Int): 0x58744
[DEBUG] 🎯 关键结果 k_in_range (Int): 0x58744
[DEBUG] 最终计算: A + k_in_range
[DEBUG] 最终私钥: 0x200000000000000000000000000058744
  📊 碰撞分析 #9:
    计算私钥: 0x200000000000000000000000000058744
    真实私钥: 0x33E7665705359F04F28B88CF897C603C9

[DEBUG] 🎯 碰撞 #10 详细分析:
[DEBUG] 区分点哈希: 0x25193
[DEBUG] 表中存储的 (y+d) 值: 0x31c5ed (3261933)
[DEBUG] 累积标量 (z+d_search): 0x82 (130)
[DEBUG] 转换为有符号: table_log=3261933, accumulated=130
[DEBUG] k_raw (有符号): 3261803
[DEBUG] k_raw (十六进制): 0x31c56b
[DEBUG] 区间长度 L: 0x200000000000000000000000000000000
[DEBUG] 区间起点 A: 0x200000000000000000000000000000000
[DEBUG] k_raw (Int): 0x31C56B
[DEBUG] 🎯 关键结果 k_in_range (Int): 0x31C56B
[DEBUG] 最终计算: A + k_in_range
[DEBUG] 最终私钥: 0x20000000000000000000000000031C56B
  📊 碰撞分析 #10:
    计算私钥: 0x20000000000000000000000000031C56B
    真实私钥: 0x33E7665705359F04F28B88CF897C603C9

[DEBUG] 🎯 碰撞 #11 详细分析:
[DEBUG] 区分点哈希: 0xd7886
[DEBUG] 表中存储的 (y+d) 值: 0x47e501 (4711681)
[DEBUG] 累积标量 (z+d_search): 0x8d (141)
[DEBUG] 转换为有符号: table_log=4711681, accumulated=141
[DEBUG] k_raw (有符号): 4711540
[DEBUG] k_raw (十六进制): 0x47e474
[DEBUG] 区间长度 L: 0x200000000000000000000000000000000
[DEBUG] 区间起点 A: 0x200000000000000000000000000000000
[DEBUG] k_raw (Int): 0x47E474
[DEBUG] 🎯 关键结果 k_in_range (Int): 0x47E474
[DEBUG] 最终计算: A + k_in_range
[DEBUG] 最终私钥: 0x20000000000000000000000000047E474
  📊 碰撞分析 #11:
    计算私钥: 0x20000000000000000000000000047E474
    真实私钥: 0x33E7665705359F04F28B88CF897C603C9

[DEBUG] 🎯 碰撞 #12 详细分析:
[DEBUG] 区分点哈希: 0x25c295
[DEBUG] 表中存储的 (y+d) 值: 0x1ba6d9 (1812185)
[DEBUG] 累积标量 (z+d_search): 0x90 (144)
[DEBUG] 转换为有符号: table_log=1812185, accumulated=144
[DEBUG] k_raw (有符号): 1812041
[DEBUG] k_raw (十六进制): 0x1ba649
[DEBUG] 区间长度 L: 0x200000000000000000000000000000000
[DEBUG] 区间起点 A: 0x200000000000000000000000000000000
[DEBUG] k_raw (Int): 0x1BA649
[DEBUG] 🎯 关键结果 k_in_range (Int): 0x1BA649
[DEBUG] 最终计算: A + k_in_range
[DEBUG] 最终私钥: 0x2000000000000000000000000001BA649
  📊 碰撞分析 #12:
    计算私钥: 0x2000000000000000000000000001BA649
    真实私钥: 0x33E7665705359F04F28B88CF897C603C9

[DEBUG] 🎯 碰撞 #13 详细分析:
[DEBUG] 区分点哈希: 0x2bbaa
[DEBUG] 表中存储的 (y+d) 值: 0x47e501 (4711681)
[DEBUG] 累积标量 (z+d_search): 0x9f (159)
[DEBUG] 转换为有符号: table_log=4711681, accumulated=159
[DEBUG] k_raw (有符号): 4711522
[DEBUG] k_raw (十六进制): 0x47e462
[DEBUG] 区间长度 L: 0x200000000000000000000000000000000
[DEBUG] 区间起点 A: 0x200000000000000000000000000000000
[DEBUG] k_raw (Int): 0x47E462
[DEBUG] 🎯 关键结果 k_in_range (Int): 0x47E462
[DEBUG] 最终计算: A + k_in_range
[DEBUG] 最终私钥: 0x20000000000000000000000000047E462
  📊 碰撞分析 #13:
    计算私钥: 0x20000000000000000000000000047E462
    真实私钥: 0x33E7665705359F04F28B88CF897C603C9

[DEBUG] 🎯 碰撞 #14 详细分析:
[DEBUG] 区分点哈希: 0x13ccdf
[DEBUG] 表中存储的 (y+d) 值: 0x587c5 (362437)
[DEBUG] 累积标量 (z+d_search): 0xa0 (160)
[DEBUG] 转换为有符号: table_log=362437, accumulated=160
[DEBUG] k_raw (有符号): 362277
[DEBUG] k_raw (十六进制): 0x58725
[DEBUG] 区间长度 L: 0x200000000000000000000000000000000
[DEBUG] 区间起点 A: 0x200000000000000000000000000000000
[DEBUG] k_raw (Int): 0x58725
[DEBUG] 🎯 关键结果 k_in_range (Int): 0x58725
[DEBUG] 最终计算: A + k_in_range
[DEBUG] 最终私钥: 0x200000000000000000000000000058725
  📊 碰撞分析 #14:
    计算私钥: 0x200000000000000000000000000058725
    真实私钥: 0x33E7665705359F04F28B88CF897C603C9

[DEBUG] 🎯 碰撞 #15 详细分析:
[DEBUG] 区分点哈希: 0x1fec8b
[DEBUG] 表中存储的 (y+d) 值: 0x1ba6d9 (1812185)
[DEBUG] 累积标量 (z+d_search): 0xab (171)
[DEBUG] 转换为有符号: table_log=1812185, accumulated=171
[DEBUG] k_raw (有符号): 1812014
[DEBUG] k_raw (十六进制): 0x1ba62e
[DEBUG] 区间长度 L: 0x200000000000000000000000000000000
[DEBUG] 区间起点 A: 0x200000000000000000000000000000000
[DEBUG] k_raw (Int): 0x1BA62E
[DEBUG] 🎯 关键结果 k_in_range (Int): 0x1BA62E
[DEBUG] 最终计算: A + k_in_range
[DEBUG] 最终私钥: 0x2000000000000000000000000001BA62E
  📊 碰撞分析 #15:
    计算私钥: 0x2000000000000000000000000001BA62E
    真实私钥: 0x33E7665705359F04F28B88CF897C603C9

[DEBUG] 🎯 碰撞 #16 详细分析:
[DEBUG] 区分点哈希: 0x12dc59
[DEBUG] 表中存储的 (y+d) 值: 0x47e501 (4711681)
[DEBUG] 累积标量 (z+d_search): 0xaf (175)
[DEBUG] 转换为有符号: table_log=4711681, accumulated=175
[DEBUG] k_raw (有符号): 4711506
[DEBUG] k_raw (十六进制): 0x47e452
[DEBUG] 区间长度 L: 0x200000000000000000000000000000000
[DEBUG] 区间起点 A: 0x200000000000000000000000000000000
[DEBUG] k_raw (Int): 0x47E452
[DEBUG] 🎯 关键结果 k_in_range (Int): 0x47E452
[DEBUG] 最终计算: A + k_in_range
[DEBUG] 最终私钥: 0x20000000000000000000000000047E452
  📊 碰撞分析 #16:
    计算私钥: 0x20000000000000000000000000047E452
    真实私钥: 0x33E7665705359F04F28B88CF897C603C9

[DEBUG] 🎯 碰撞 #17 详细分析:
[DEBUG] 区分点哈希: 0xda527
[DEBUG] 表中存储的 (y+d) 值: 0x1ba6d9 (1812185)
[DEBUG] 累积标量 (z+d_search): 0xb2 (178)
[DEBUG] 转换为有符号: table_log=1812185, accumulated=178
[DEBUG] k_raw (有符号): 1812007
[DEBUG] k_raw (十六进制): 0x1ba627
[DEBUG] 区间长度 L: 0x200000000000000000000000000000000
[DEBUG] 区间起点 A: 0x200000000000000000000000000000000
[DEBUG] k_raw (Int): 0x1BA627
[DEBUG] 🎯 关键结果 k_in_range (Int): 0x1BA627
[DEBUG] 最终计算: A + k_in_range
[DEBUG] 最终私钥: 0x2000000000000000000000000001BA627
  📊 碰撞分析 #17:
    计算私钥: 0x2000000000000000000000000001BA627
    真实私钥: 0x33E7665705359F04F28B88CF897C603C9

[DEBUG] 🎯 碰撞 #18 详细分析:
[DEBUG] 区分点哈希: 0x12860f
[DEBUG] 表中存储的 (y+d) 值: 0x1ba6d9 (1812185)
[DEBUG] 累积标量 (z+d_search): 0xb5 (181)
[DEBUG] 转换为有符号: table_log=1812185, accumulated=181
[DEBUG] k_raw (有符号): 1812004
[DEBUG] k_raw (十六进制): 0x1ba624
[DEBUG] 区间长度 L: 0x200000000000000000000000000000000
[DEBUG] 区间起点 A: 0x200000000000000000000000000000000
[DEBUG] k_raw (Int): 0x1BA624
[DEBUG] 🎯 关键结果 k_in_range (Int): 0x1BA624
[DEBUG] 最终计算: A + k_in_range
[DEBUG] 最终私钥: 0x2000000000000000000000000001BA624
  📊 碰撞分析 #18:
    计算私钥: 0x2000000000000000000000000001BA624
    真实私钥: 0x33E7665705359F04F28B88CF897C603C9
❌ 在 1 次迭代内未找到私钥

📊 搜索统计信息:
  搜索尝试次数: 1
  总步数: 100
  总碰撞次数: 18
  误报次数: 18
  搜索时间: 0.16 秒
  平均步速: 624 步/秒
  碰撞率: 18.000000%
  误报率: 100.00%

📋 步骤6: 验证搜索结果...
❌ 搜索失败
  可能原因:
  1. 迭代次数不足
  2. 预计算表覆盖范围不足
  3. 算法参数需要调整

📋 步骤7: 最终统计信息...

📊 搜索统计信息:
  搜索尝试次数: 1
  总步数: 100
  总碰撞次数: 18
  误报次数: 18
  搜索时间: 0.17 秒
  平均步速: 604 步/秒
  碰撞率: 18.000000%
  误报率: 100.00%

🎉 Bernstein-Lange搜索测试完成!
==========================================
⚠️ 测试未完全成功 - 需要进一步调试
📊 总测试时间: 3034 毫秒

🔄 下一步工作:
1. 如果测试成功：准备130号谜题验证
2. 如果测试失败：调试算法参数和实现
3. 优化性能和扩展到更大问题
