# 通用 AI Agent 合作守则（全局规则）
---

## 1  四步必做流程（无例外）
每次对话按顺序执行一次：  
1. **Context7** — 收集最新技术资料  
2. **Sequential Thinking** — 结构化拆解问题  
3. **MCP Feedback Enhanced** — 给出选项，等待确认  
4. **Memory** — 把本轮关键信息写入记忆图谱  

---

## 2  防重复代码规则
| 动作 | 要求 |
|---|---|
| **搜索先行** | 生成函数/类/配置前，先全局搜索 **前缀关键字** |
| **相似即复用** | 相似度 ≥ 90 % 直接复用，禁止再写 |
| **命名前缀** | 按项目约定前缀（如 `util_`、`core_`）；新建加 `// NEW: 原因` |
| **禁止重复** | 禁止 `my_*_v2`、`xxx_v3` 等无意义变体 |

---

## 3  删除文件
必须先弹 **MCP Feedback Enhanced** 窗口，**得到明确同意** 后执行。

---

## 4  终止条件
仅当我输入 `结束` / `可以了` / `无需继续` / `停止回答` 方可停止循环。

---

## 5  Memory 模板
在 MCP Feedback Enhanced 结束前，一次性写入：

```json
{
  "entities": [
    {
      "name": "<项目/模块名>",
      "entityType": "project|module|function",
      "observations": ["一句话总结本轮关键信息"]
    }
  ]
}