/**
 * @file uint512_unified.cuh
 * @brief 统一的512-bit整数定义 - 消除重复代码
 * 
 * 这个文件提供统一的512-bit整数结构和基础运算
 * 替代在多个文件中重复定义的uint512_t
 */

#ifndef UINT512_UNIFIED_CUH
#define UINT512_UNIFIED_CUH

#include <cuda_runtime.h>
#include <cstdint>

/**
 * @brief 统一的512-bit整数结构
 * 
 * 替代在以下文件中的重复定义：
 * - optimizations/phase2/kang_per_sm_kernel.cu
 * - optimizations/phase3/montgomery_math_512.cuh
 * - optimizations/phase3/sharded_hashtable.cuh
 */
struct uint512_t {
    uint64_t d[8];  // 8个64位整数 = 512位
    
    __device__ __host__ __forceinline__ uint512_t() {
        #ifdef __CUDA_ARCH__
        #pragma unroll
        #endif
        for(int i = 0; i < 8; i++) d[i] = 0;
    }

    __device__ __host__ __forceinline__ uint512_t(uint64_t val) {
        d[0] = val;
        #ifdef __CUDA_ARCH__
        #pragma unroll
        #endif
        for(int i = 1; i < 8; i++) d[i] = 0;
    }
    
    __device__ __host__ __forceinline__ uint512_t& operator+=(const uint512_t& other) {
        uint64_t carry = 0;
        #ifdef __CUDA_ARCH__
        #pragma unroll
        #endif
        for(int i = 0; i < 8; i++) {
            uint64_t sum = d[i] + other.d[i] + carry;
            carry = (sum < d[i]) ? 1 : 0;
            d[i] = sum;
        }
        return *this;
    }

    __device__ __host__ __forceinline__ uint512_t& operator-=(const uint512_t& other) {
        uint64_t borrow = 0;
        #ifdef __CUDA_ARCH__
        #pragma unroll
        #endif
        for(int i = 0; i < 8; i++) {
            uint64_t temp = d[i] - other.d[i] - borrow;
            borrow = (temp > d[i]) ? 1 : 0;
            d[i] = temp;
        }
        return *this;
    }
    
    __device__ __host__ __forceinline__ bool operator==(const uint512_t& other) const {
        #ifdef __CUDA_ARCH__
        #pragma unroll
        #endif
        for(int i = 0; i < 8; i++) {
            if(d[i] != other.d[i]) return false;
        }
        return true;
    }

    __device__ __host__ __forceinline__ bool operator!=(const uint512_t& other) const {
        return !(*this == other);
    }

    __device__ __host__ __forceinline__ bool is_zero() const {
        #ifdef __CUDA_ARCH__
        #pragma unroll
        #endif
        for(int i = 0; i < 8; i++) {
            if(d[i] != 0) return false;
        }
        return true;
    }
    
    __device__ __host__ __forceinline__ void set_zero() {
        #pragma unroll
        for(int i = 0; i < 8; i++) d[i] = 0;
    }
    
    __device__ __host__ __forceinline__ uint512_t operator+(const uint512_t& other) const {
        uint512_t result = *this;
        result += other;
        return result;
    }
    
    __device__ __host__ __forceinline__ uint512_t operator-(const uint512_t& other) const {
        uint512_t result = *this;
        result -= other;
        return result;
    }

    /**
     * @brief 比较两个512-bit整数
     * @return -1 if this < other, 0 if equal, 1 if this > other
     */
    __device__ __forceinline__
    int compare(const uint512_t& other) const {
        for(int i = 7; i >= 0; i--) {
            if(d[i] > other.d[i]) return 1;
            if(d[i] < other.d[i]) return -1;
        }
        return 0;
    }

    /**
     * @brief 左移一位
     */
    __device__ __forceinline__
    void shift_left_1() {
        uint64_t carry = 0;
        for(int i = 0; i < 8; i++) {
            uint64_t new_carry = (d[i] >> 63) & 1;
            d[i] = (d[i] << 1) | carry;
            carry = new_carry;
        }
    }

    /**
     * @brief 右移一位
     */
    __device__ __forceinline__
    void shift_right_1() {
        uint64_t carry = 0;
        for(int i = 7; i >= 0; i--) {
            uint64_t new_carry = d[i] & 1;
            d[i] = (d[i] >> 1) | (carry << 63);
            carry = new_carry;
        }
    }

    /**
     * @brief 检查是否为1
     */
    __device__ __forceinline__
    bool is_one() const {
        if(d[0] != 1) return false;
        for(int i = 1; i < 8; i++) {
            if(d[i] != 0) return false;
        }
        return true;
    }

    /**
     * @brief 设置为1
     */
    __device__ __forceinline__
    void set_one() {
        d[0] = 1;
        for(int i = 1; i < 8; i++) {
            d[i] = 0;
        }
    }

    /**
     * @brief 检查是否为负数（用于模逆算法）
     */
    __device__ __forceinline__
    bool is_negative() const {
        return (d[7] & 0x8000000000000000ULL) != 0;
    }
};

/**
 * @brief secp256k1椭圆曲线常数 - 统一定义 (仅声明)
 */
namespace secp256k1_constants {
    // secp256k1模数: p = 2^256 - 2^32 - 2^9 - 2^8 - 2^7 - 2^6 - 2^4 - 1
    extern __device__ __constant__ uint64_t P_DATA[8];

    // 椭圆曲线参数 a = 0, b = 7
    extern __device__ __constant__ uint64_t A_DATA[8];
    extern __device__ __constant__ uint64_t B_DATA[8];
}

/**
 * @brief 辅助函数：从数组创建uint512_t
 */
__device__ __host__ __forceinline__
uint512_t make_uint512(const uint64_t data[8]) {
    uint512_t result;
    #ifdef __CUDA_ARCH__
    #pragma unroll
    #endif
    for(int i = 0; i < 8; i++) {
        result.d[i] = data[i];
    }
    return result;
}

/**
 * @brief 辅助函数：比较两个uint512_t
 * @return -1 if a < b, 0 if a == b, 1 if a > b
 */
__device__ __host__ __forceinline__
int compare_uint512(const uint512_t& a, const uint512_t& b) {
    #ifdef __CUDA_ARCH__
    #pragma unroll
    #endif
    for(int i = 7; i >= 0; i--) {
        if(a.d[i] > b.d[i]) return 1;
        if(a.d[i] < b.d[i]) return -1;
    }
    return 0;
}

/**
 * @brief 辅助函数：复制uint512_t
 */
__device__ __host__ __forceinline__
void copy_uint512(uint512_t& dest, const uint512_t& src) {
    #pragma unroll
    for(int i = 0; i < 8; i++) {
        dest.d[i] = src.d[i];
    }
}

/**
 * @brief 调试函数：打印uint512_t (仅主机端)
 */
#ifndef __CUDA_ARCH__
inline void print_uint512(const uint512_t& val, const char* name = nullptr) {
    if(name) printf("%s: ", name);
    printf("0x");
    for(int i = 7; i >= 0; i--) {
        printf("%016llx", val.d[i]);
    }
    printf("\n");
}
#endif

#endif // UINT512_UNIFIED_CUH
