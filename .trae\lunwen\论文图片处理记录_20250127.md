由于论文原文较长（22页），我将严格按照您的要求，分部分逐段提供**一字不差**的原文复制。以下是第一部分（标题、作者、摘要和引言部分）：

---

### 标题与作者信息  
**Computing small discrete logarithms faster**  
<PERSON>\(^{1,2}\) and <PERSON><PERSON>\(^{2}\)  
\(^{1}\) Department of Computer Science  
University of Illinois at Chicago, Chicago, IL 60607-7053, USA  
<EMAIL>  
\(^{2}\) Department of Mathematics and Computer Science  
Technische Universiteit Eindhoven, P.O. Box 513, 5600 MB Eindhoven, the Netherlands  
<EMAIL>  

---

### Abstract  
**Abstract.** Computations of small discrete logarithms are feasible even in "secure" groups, and are used as subroutines in several cryptographic protocols in the literature. For example, the Boneh-Goh-Nissim degree-2-homomorphic public-key encryption system uses generic square-root discrete-logarithm methods for decryption. This paper shows how to use a small group-specific table to accelerate these subroutines. The cost of setting up the table grows with the table size, but the acceleration also grows with the table size. This paper shows experimentally that computing a discrete logarithm in an interval of order \(\ell\) takes only \(1.93 \cdot \ell^{1/3}\) multiplications on average using a table of size \(\ell^{1/3}\) precomputed with \(1.21 \cdot \ell^{2/3}\) multiplications, and computing a discrete logarithm in a group of order \(\ell\) takes only \(1.77 \cdot \ell^{1/3}\) multiplications on average using a table of size \(\ell^{1/3}\) precomputed with \(1.24 \cdot \ell^{2/3}\) multiplications.  

**Keywords:** Discrete logarithms, random walks, precomputation.  

---

### 1. Introduction  
Fully homomorphic encryption is still prohibitively slow, but there are much more efficient schemes achieving more limited forms of homomorphic encryption. We highlight Freeman's variant [11] of the scheme by Boneh, Goh, and Nissim [7]. The Boneh-Goh-Nissim (BGN) scheme can handle adding arbitrary subsets of encrypted data, multiplying the sums, and adding any number of the products. Freeman's variant works in groups typically encountered in pairing-based protocols. The scheme is vastly more efficient than schemes handling unlimited numbers of additions and multiplications. Encryption takes only one exponentiation, as does addition of encrypted messages; multiplication takes a pairing computation.  

The limitation to one level of multiplication means that polynomial expressions of degree at most 2 can be evaluated over the encrypted messages, but this is sufficient for a variety of protocols. For example, [7] presented protocols for private information retrieval, elections, and generally universally verifiable computation. There are 395 citations of [7] so far, according to Google Scholar.  

The BGN protocol does not have any built-in limit on the number of ciphertexts added, but it does take more time to decrypt as this number grows. The problem is that decryption requires computing a discrete logarithm, where the message is the unknown exponent. If this message is a sum of \(B\) products of sums of \(A\) input messages from the space \(\{0, \ldots, M\}\), then the final message can be essentially anywhere in the interval \([0, (AM)^2 B]\). This means that even if the space for the input messages is limited to bits \(\{0,1\}\), the discrete-logarithm computation needs to be able to handle the interval \([0, A^2 B]\). For "random" messages the result is almost certainly in a much shorter interval, but most applications need to be able to handle non-random messages.  

Boneh, Goh, and Nissim suggested using Pollard's kangaroo method for the discrete-logarithm computation. This method runs in time \(\Theta(\ell^{1/2})\) for an interval of size \(\ell\). This bottleneck becomes quite troublesome as \(A\) and \(B\) grow.  

For larger message spaces, Hu, Martin, and Sunar in [18] sped up the discrete-logarithm computation at the cost of expanding the ciphertext length and slowing down encryption and operations on encrypted messages. They suggested representing the initial messages by their residues modulo small coprime numbers \(d_1, \ldots, d_j\) with \(\prod d_i > (AM)^2 B\), and encrypting these \(j\) residues separately. This means that the ciphertexts are \(j\) times as long and that each operation on the encrypted messages is replaced by \(j\) operations of the same type on the components. The benefit is that each discrete logarithm is limited to \([0, (A d_i)^2 B]\), which is a somewhat smaller interval. The original messages are reconstructed using the Chinese remainder theorem.  

---
以下是第2节内容：

---

## 2. Review of generic discrete-logarithm algorithms

This section reviews several standard "square-root" methods to compute discrete logarithms in a group of prime order ℓ. Throughout this paper we write the group operation multiplicatively, write g for the standard generator of the group, and write h for the DLP input; our objective is thus to compute log₉ h, i.e., the unique integer k modulo ℓ such that h = gᵏ.

All of these methods are "generic": they work for any order-ℓ group, given an oracle for multiplication (and assuming sufficient hash randomness, for the methods using a hash function). "Square-root" means that the algorithms take Θ(ℓ¹ᐟ²) multiplications on average over all group elements h.

### Shanks's baby-step-giant-step method.
The baby-step-giant-step method [31] computes ⌈ℓ/W⌉ "giant steps" g⁰, gᵂ, g²ᵂ, g³ᵂ,... and then computes a series of W "baby steps" h,hg,hg²,...,hgᵂ⁻¹. Here W is an algorithm parameter. It is easy to see that there will be a collision gⁱᵂ = hgʲ, revealing log₉ h = iW - j.

Normally W is chosen as Θ(ℓ¹ᐟ²), so that there are O(ℓ¹ᐟ²) multiplications in total; more precisely, as (1 + o(1))ℓ¹ᐟ² so that there are ≤ (2 + o(1))ℓ¹ᐟ² multiplications in total. Interleaving baby steps with giant steps, as suggested by Pollard in [29, page 439, top], obtains a collision after (4/3 + o(1))ℓ¹ᐟ² multiplications on average. We have recently introduced a "two grumpy giants and a baby" variant that reduces the constant 4/3; see [5].

The standard criticism of these methods is that they use a large amount of memory, around ℓ¹ᐟ² group elements. One can reduce the giant-step storage to, e.g., Θ(ℓ¹ᐟ³) group elements by taking W as Θ(ℓ²ᐟ³), but this also increases the average number of baby steps to Θ(ℓ²ᐟ³). This criticism is addressed by the rho and kangaroo methods discussed below, which drastically reduce space usage while still using just Θ(ℓ¹ᐟ²) multiplications.

### Pollard's rho method.
Pollard's original rho method [28, Section 1] computes a pseudorandom walk 1, F(1), F(F(1)),... Here F(u) is defined as gu or u² or hu, depending on whether a hash of u is 0 or 1 or 2. Each iterate Fⁿ(1) then has the form gʸhˣ for some easily computed pair (x,y) ∈ (ℤ/ℓ)², and any collision gʸhˣ = gʸ'hˣ' with (x,y) ≠ (x',y') immediately reveals log₉ h. One expects a sufficiently random-looking walk on ℓ group elements to collide with itself within O(ℓ¹ᐟ²) steps. There are several standard methods to find the collision with negligible memory consumption.

Van Oorschot and Wiener in [35] proposed running many walks in parallel, starting from different points gʸhˣ and stopping each walk when it reaches a "distinguished point". Here a fraction 1/W of the points are defined (through another hash function) as "distinguished", where W is an algorithm parameter; each walk reaches W points on average. One checks for collisions only among the occasional distinguished points, not among all of the group elements produced. The critical observation is that if two walks reach the same group element then they will eventually reach the same distinguished point — or will enter cycles, but cycles have negligible chance of appearing if W is below the scale of ℓ¹ᐟ².

There are many other reasonable choices of F. One popular choice—when there are many walks as in [35], not when there is a single walk as in [28, Section 1]—is a "base-g r-adding walk": this means that the hash function has r different values, and F(u) is defined as s₁u or s₂u or ... or sᵣu respectively, where s₁,s₂,...,sᵣ are precomputed as random powers of g. One then starts each walk at a different power hˣ. This approach has several minor advantages (for example, x is constant in each walk and need not be updated) and the major advantage of simulating a random walk quite well as r increases. See, e.g., [30], [33], and [5] for further discussion of the impact of r. The bottom line is that this method finds a discrete logarithm within (√(π/2) + o(1))ℓ¹ᐟ² multiplications on average.

The terminology "r-adding walk" is standard in the literature but the terminology "base-g r-adding walk" is not. We use this terminology to distinguish a base-g r-adding walk from a "base-(g,h) r-adding walk", in which s₁,s₂,...,sᵣ are precomputed as products of random powers of g and h. This distinction is critical in Section 3.

### Pollard's kangaroo method.
An advantage of baby-step-giant-step, already exploited by Shanks in the paper [31] introducing the method, is that it immediately generalizes from computing discrete logarithms in any group of prime order ℓ to computing discrete logarithms in any interval of length ℓ inside any group of prime order p ≥ ℓ. The rho method uses Θ(p¹ᐟ²) group operations, often far beyond Θ(ℓ¹ᐟ²) group operations.

Pollard in [28, Section 3] introduced a "kangaroo" method that combines the advantages of the baby-step-giant-step method and the rho method: it takes only Θ(ℓ¹ᐟ²) group operations to compute discrete logarithms in an interval of length ℓ, while still using negligible memory. This method:
- chooses a base-g r-adding iteration function whose steps have average exponents Θ(ℓ¹ᐟ²), instead of exponents chosen uniformly modulo ℓ;
- runs a walk starting from gʸ (the "tame kangaroo"), where y is at the right end of the interval;
- records the Wth step in this walk (the "trap"), where W is Θ(ℓ¹ᐟ²); and
- runs a walk (the "wild kangaroo") starting from h, checking at each step whether this walk has fallen into the trap.

van Oorschot and Wiener in [35] proposed a parallel kangaroo method in which tame kangaroos start from gʸ for many values of y, all close to the middle of the interval, and a similar number of wild kangaroos start from hgʸ for many small values of y. Collisions are detected by distinguished points as in the parallel rho method, but the distinguished-point property is chosen to have probability considerably higher than 1/W; walks continue past distinguished points. The walks are adjusted to avoid collisions between tame kangaroos and to avoid collisions between wild kangaroos. Several subsequent papers have proposed refinements of the kangaroo method, obtaining constant-factor speedups.

### The Nechaev-Shoup bound.
Shoup proved in [32] that all generic discrete-logarithm algorithms have success probability O(m²/ℓ) after m multiplications. (The same bound had been proven by Nechaev in [25] for a more limited class of algorithms, which one might call "representation oblivious" generic discrete-logarithm algorithms.) All generic discrete-logarithm algorithms therefore need Ω(ℓ¹ᐟ²) multiplications on average; i.e., the usual square-root discrete-logarithm algorithms are optimal up to a constant factor. A closer look shows that the lower bound is (2√2/3 + o(1))ℓ¹ᐟ², so both the baby-step-giant-step method and the rho method are within a factor 2 + o(1) of optimal.

There are much faster discrete-logarithm algorithms (e.g., index-calculus algorithms) for specific classes of groups. However, the conventional wisdom is that these square-root algorithms are the fastest discrete-logarithm algorithms for "secure" groups: a sensibly chosen elliptic-curve group, for example, or the order-ℓ subgroup of Fₚ* for sufficiently large p.

In the rest of this paper we discuss algorithms that improve upon these square-root algorithms by a non-constant factor. Evidently these improved algorithms do not fit Shoup's model of "generic" algorithms—but these improved algorithms do apply to "secure" groups. The algorithms deviate from the "generic" model by requiring an extra input, a small table that depends on the group but not on the particular discrete logarithm being computed. The table is set up by a generic algorithm, and if one views the setup and use of the table as a single unified algorithm then Shoup's bound applies to that algorithm; but if the table is set up once and used enough times to amortize the setup costs then each use of the table evades Shoup's bound.

---
我将继续为您提供论文第3节的完整内容。以下是第3节"Using a small table to accelerate generic discrete-logarithm algorithms"的原文：

---

## 3. Using a small table to accelerate generic discrete-logarithm algorithms

This section explains how to use a small table to accelerate Pollard's rho and kangaroo methods. The table depends on the group, and on the base point g, but not on the target h. For intervals the table depends on the length of the interval but not on the position of the interval: dividing h by gᴬ reduces a discrete logarithm in the interval {A,A+1,...,A+ℓ-1} to a discrete logarithm in the interval {0,1,...,ℓ-1}, eliminating the influence of A.

The speedup factor grows as the square root of the table size T. As T grows, the average number of multiplications needed to compute a discrete logarithm drops far below the ≈ ℓ¹ᐟ² multiplications used in the previous section.

The cost of setting up the table is larger than ℓ¹ᐟ², also growing with the square root of T. However, this cost is amortized across all of the targets h handled with the same table. Comparing the table-setup cost (ℓT)¹ᐟ² to the discrete-logarithm cost (ℓ/T)¹ᐟ² shows that the table-setup cost becomes negligible as the number of targets handled grows past T.

The main parameters in this algorithm are the table size T and the walk length W. Sensible parameter choices will satisfy W ≈ α(ℓ/T)¹ᐟ², where α is a small constant discussed below. Auxiliary parameters are various decisions used in building the table; these decisions are analyzed below.

### The basic algorithm.
To build the table, simply start some walks at gʸ for random choices of y. The table entries are the distinct distinguished points produced by these walks, together with their discrete logarithms.

It is critical here for the iteration function used in the walks to be independent of h. A standard base-g r-adding walk satisfies this condition, and for simplicity we focus on the case of a base-g r-adding walk, although we recommend that implementors also try "mixed walks" with some squarings. Sometimes walks collide (this happens frequently when parameters are chosen sensibly), so setting up the table requires more than T walks; see below for quantification of this effect.

To find the discrete logarithm of h using this table, start walks at hˣ for random choices of x, producing various distinguished points hˣgʸ, exactly as in the usual rho method. Check for two of these new distinguished points colliding, but also check for one of these new distinguished points colliding with one of the distinguished points in the precomputed table. Any such collision immediately reveals log₉ h.

In effect, the table serves as a free foundation for the list of distinguished points naturally accumulated by the algorithm. If the number of h-dependent walks is small compared to T (this happens when parameters are chosen sensibly) then one can reasonably skip the check for two of the new distinguished points colliding; the algorithm almost always succeeds from collisions with distinguished points in the precomputed table.

### Special cases.
The extreme case T = 0 of this algorithm is the usual rho method with a base-g r-adding walk (or, more generally, the rho method with any h-independent iteration function). However, our main interest is in the speedups provided by larger values of T.

We also draw attention to the extreme case r = 1 with exponent 1, simply stepping from u to gu. In this case the main "rho" computation consists of taking, on average, W baby steps hˣ,hˣg,hˣg²,... and then looking up the resulting distinguished point in a table. What is interesting about this case is its evident similarity to the baby-step-giant-step method, but with the advantage of carrying out a table access only after W baby steps; the usual baby-step-giant-step method checks the table after every baby step. What is bad about this case is that the walk is highly nonrandom, requiring Θ(ℓ) steps to collide with another such walk; larger values of r create collisions within Θ(ℓ¹ᐟ²) steps.

Recall from Section 1 the classic algorithm to solve multiple discrete logarithms: for each k in turn, compute log₉ hₖ with the rho method, reusing the distinguished points produced by h₁,...,hₖ₋₁. The log₉ hₖ part of this computation obviously fits the algorithm discussed here, with T implicitly defined as the number of distinguished points produced by h₁,...,hₖ₋₁. We emphasize, however, that this is a special choice of T, and that the parameter curve (T,W) used implicitly in this algorithm as k varies does not obey the relationship W ≈ α(ℓ/T)¹ᐟ² mentioned above. Treating T and W as explicit parameters allows several optimizations that we discuss below.

### Optimizing the walk length.
Assume that W ≈ α(ℓ/T)¹ᐟ², and consider the chance that a single walk already encounters one of the T distinguished points in the table, thereby solving the DLP. The T table entries were obtained from walks that, presumably, each covered about W points, for a total of TW points. The new walk also covers about W points and thus has TW² ≈ α²ℓ collision opportunities. If these collision opportunities were independent then the chance of escaping all of these collisions would be (1-1/ℓ)ᵃ²ˡ ≈ exp(-α²).

This heuristic analysis suggests that a single walk succeeds with, e.g., probability 1-exp(-1/16) ≈ 6% for α = 1/4, or probability 1-exp(-1/4) ≈ 22% for α = 1/2, or probability 1-exp(-1) ≈ 63% for α = 1, or probability 1-exp(-4) ≈ 98% for α = 2.

The same analysis also suggests that the end of the precomputation, finding the Tth point in the table, will require trying exp(1/16) ≈ 1.06 length-W walks for α = 1/4, or exp(1/4) ≈ 1.28 length-W walks for α = 1/2, or exp(1) ≈ 2.72 length-W walks for α = 1, or exp(4) ≈ 54.6 length-W walks for α = 2.

The obvious advantage of taking very small α is that one can reasonably carry out several walks in parallel. Taking (e.g.) α = 1/8 requires 64 walks on average, and if one carries out (e.g.) 4 walks in parallel then at most 3 walks are wasted. The most common argument for parallelization is that it allows the computation to exploit multiple cores, decreasing latency. Parallelization is helpful even when latency is not a concern: for example, it allows merging inversions in affine elliptic-curve computations (Montgomery's trick), and it often allows effective use of vector units in a single core. Solving many independent discrete-logarithm problems produces the same benefits, but requires the application to have many independent problems ready at the same time.

The obvious disadvantage of taking very small α is that the success probability per walk drops quadratically with α, while the walk length drops only linearly with α. In other words, chopping a small α in half makes each step half as effective, doubling the number of steps expected in the computation. Sometimes this is outweighed by the increase in parallelization (there are now four times as many walks), but clearly there is a limit to how small α can reasonably be taken.

Clearly there is also a limit to how large α can reasonably be taken. Doubling α beyond 1 does not make each step twice as effective: an α = 1 walk already succeeds with chance 63%; an α = 2 walk succeeds with chance 98% but is twice as expensive.

We actually recommend optimizing α experimentally (and not limiting it to powers of 2), rather than trusting the exact details of the heuristic analysis shown above. A small issue with the heuristic analysis is that the new walk sometimes takes only, say, W/2 steps, obtaining collisions with much lower probability than indicated above, and sometimes 2W steps; the success probability of a walk is not the same as the success probability of a length-W walk. A larger issue is that TW is only a crude approximation to the table coverage. Discarding previously discovered distinguished points when building the table creates a bias towards short walks, especially for large α; on the other hand, a walk finding a distinguished point will rarely see all of the ancestors of that point, and in a moment we will see that this is a controllable effect, allowing the table coverage to be significantly increased.

Lee, Cheon, and Hong in [22, Lemma 1 and Theorem 1] give a detailed heuristic argument that starting M walks in the precomputation will produce T ≈ M(√(1+2a)-1)/a distinct distinguished points, where a = MW²/ℓ (so our α is (√(1+2a)-1)¹ᐟ²), and that each walk in the main computation then succeeds with probability 1-1/√(1+2a) (i.e., 1-1/(α²+1)). In [22, page 13] they recommend taking a = (1+√5)/4 ≈ 0.809 (equivalently, α ≈ 0.786); the heuristics then state that T ≈ 0.764M and that each walk in the main computation succeeds with probability 1-1/√(1+2a) ≈ 0.382, so the main computation uses W/0.382 ≈ 2.058(ℓ/T)¹ᐟ² multiplications on average. We issue three cautions regarding this recommendation. First, assuming the same heuristics, it is actually better to take a = 1.5 (equivalently, α = 1); then the main computation uses just 2(ℓ/T)¹ᐟ² multiplications on average. Second, our improvements to the table coverage (see below) reduce the number of multiplications, and this reduction is different for different choices of a (see our experimental results in Section 4), rendering the detailed optimization in [22] obsolete. Third, even though we emphasize number of multiplications as a simple algorithm metric, the real goal is to minimize time; the parallelization issues discussed above seem to favor considerably smaller choices of α, depending on the platform.

### Choosing the most useful distinguished points.
Instead of randomly generating T distinguished points, we propose generating more distinguished points, say 2T or 10T or 1000T, and then keeping the T most useful distinguished points. (This presumably means storing 2T or 10T or 1000T points during the precomputation, but we follow standard practice in distinguishing between the space consumed during the precomputation and the space required for the output of the precomputation. As an illustrative example in support of this practice, consider the precomputed rainbow tables distributed by the A5/1 Cracking Project [26]; the cost of local RAM used temporarily by those computations is much less important than the network cost of distributing these tables to users and the long-term cost of storing these tables.)

The natural definition of "most useful" is "having the largest number of ancestors". By definition the ancestors of a distinguished point are the group elements that walk to this point; the chance of a uniform random group element walking to this point is exactly the number of ancestors divided by ℓ.

Unfortunately, without taking the time to survey all ℓ group elements, one does not know the number of ancestors of a distinguished point. Fortunately, one has a statistical estimate of this number: a distinguished point found by many walks is very likely to be more useful than a distinguished point found by fewer walks. This estimate is unreliable for a distinguished point found by very few walks, especially for distinguished points found by just one walk; we thus propose using the walk length as a secondary estimate. (In our experiments we computed a weight for each distinguished point as the total length of all walks reaching the point, plus 4W per walk; we have not yet experimented with modifications to this weighting.) This issue disappears as the number of random walks increases towards larger multiples of T.

This table-generation strategy reduces the number of walks required for the main discrete-logarithm computation. The table still has size T, and each walk still has average length W, but the success probability of each walk increases. The only disadvantage is an increase in the time spent setting up the table.

### Interlude: the penalty for iteration functions that depend on h.
Escott, Sager, Selkirk, and Tsapakidis in [9, Section 4.4] chose an iteration function "that is independent of all the Qᵢs" (the targets hᵢ): namely, a base-g r-adding walk, optionally mixed with squarings. Kuhn and Struik in [20] said nothing about this independence condition; instead they chose a base-(g,hₖ) r-adding walk. See [20, Section 2.2] ("gᵃ¹hᵇ¹") and [20, Section 4] ("all distinguished points gᵃʲhᵢᵇʲ that were calculated in order to find xᵢ"). No experiments were reported in [20], except for a brief comment in [20, Remark 2] that the running-time estimate in [20, Theorem 1] was "a good approximation of practically observed values".

Hitchcock, Montague, Carter, and Dawson in [17, page 89] pointed out that "the particular random walk recommended by Kuhn and Struik", with the iteration function used for hₖ different from the iteration functions used for h₁,...,hₖ₋₁, fails to detect collisions "from different random walks". They reported experiments showing that a base-(g,hₖ) r-adding walk was much less effective for multiple discrete logarithms than a base-g r-adding walk.

To understand this penalty, consider the probability that the main computation succeeds with one walk, i.e., that the resulting distinguished point appears in the table. There are ≈ ℓ/W distinguished points, and the table contains T of those points, so the obvious first approximation is that the main computation succeeds with probability TW/ℓ. If the table is generated by a random walk independent of the walk used in the main computation then this approximation is quite reasonable. If the table was generated by the same walk used in the main computation then the independence argument no longer applies and the approximation turns out to be a severe underestimate.

In effect, the table-generation process in [20] selects the table entries uniformly at random from the set of distinguished points. The table-generation process in [9], [17], and [22] instead starts from random group elements and walks to distinguished points; this produces a highly non-uniform distribution of distinguished points covered by the table, biasing the table entries towards more useful distinguished points. We go further, biasing the table entries even more by selecting them carefully from a larger pool of distinguished points.

### Choosing the most useful iteration function.
Another useful way to spend more time on table setup is to try different iteration functions, i.e., different choices of exponents for the r-adding walk.

The following examples are a small illustration of the impact of varying the iteration function. http://cr.yp.to/dlog/20120727-function1.pdf is a directed graph on 1000 nodes obtained as follows. Each node marked itself as distinguished with probability 1/W where W = 10. (We did not enforce exactly 100 distinguished points; each node made its decision independently.) Each non-distinguished node created an outgoing edge to a uniform random node. We then used the neato program, part of the standard graphviz package [13], to draw the digraph with short edges. The T = 10 most useful distinguished points are black squares; the 593 nontrivial ancestors of those points are black circles; the other 99 distinguished points are white squares; the remaining points are white circles.

http://cr.yp.to/dlog/20120727-function2.pdf is another directed graph obtained in the same way, with the same values of W and T but different distinguished points and a different random walk. For this graph the 10 most useful distinguished points have 687 nontrivial ancestors, for an overall success probability of 697/1000 ≈ 70%, significantly better than the first graph and also significantly above the 63% heuristic mentioned earlier.

These graphs were not selected as outliers; they were the first two graphs we generated. Evidently the table coverage has rather high variance.

Of course, a larger table coverage by itself does not imply better performance: graphs with larger coverage tend to have longer walks. We thus use the actual performance of the resulting discrete-logarithm computations as a figure of merit for the graph.

For small examples it is easy to calculate the exact average-case performance, rather than just estimate it statistically. Our second sample graph uses, on average, 10.8506 steps to compute a discrete logarithm if walks are limited to 27 steps. (Here 27 is optimal for that graph. The graph has cycles, so some limit or other cycle-detection mechanism is required. One can also take this limit into account in deciding which distinguished points are best.) Our first sample graph uses, on average, 11.2007 steps.

### Adapting the method to a small interval
We now explain a small set of tweaks that adapt the basic algorithm stated above to the problem of computing discrete logarithms in an interval of length ℓ. These tweaks trivially combine with the refinements stated above, such as choosing the most useful distinguished points.

As in the standard kangaroo method, choose the steps s₁,s₂,...,sᵣ as powers of g where the exponents are βℓ/W on average. We recommend numerical optimization of the constant β.

Start walks at gʸ for random choices of y in the interval. As in the basic algorithm, stop each walk when it reaches a distinguished point, and build a table of discrete logarithms of the resulting distinguished points.

To find the discrete logarithm of h, start a walk at hgʸ for a random small integer y; stop at the first distinguished point; and check whether the resulting distinguished point is in the table. In our experiments we defined "small" as "bounded by ℓ/256", but it would also have been reasonable to start the first walk at h, the second at hg, the third at hg², etc.

We are deviating in several ways here from the typical kangaroo methods stated in the literature. Our walks starting from gʸ can be viewed as tame kangaroos, but our tame kangaroos are spread through the interval rather than being clustered together. We do not continue walks past distinguished points. We select the most useful distinguished points experimentally, rather than through preconceived notions of how far kangaroos should be allowed to jump.

We do not claim that the details of this approach are optimal. However, this approach has the virtue of being very close to the basic algorithm, and our experiments so far have found discrete logarithms in intervals of length ℓ almost as quickly as discrete logarithms in groups of order ℓ.

---

我将继续为您提供论文第4节"Experiments"的完整内容。以下是该节的原文：

---

## 4. Experiments

This section reports several experiments with the algorithm described in Section 3, both for small groups and for small intervals inside larger groups. To aid in verification we have posted our software for a typical small-interval experiment at http://cr.yp.to/dlog/cuberoot.html.

### Case study: a small-group experiment.
We began with several experiments targeting the discrete-logarithm problem modulo pq described in [15, Table 2, first line]. Here p and q are "768-bit primes" generated so that p-1 and q-1 are "2⁴⁸-smooth"; presumably this means that (p-1)/2 is a product of 16 primes slightly below 2⁴⁸, and similarly for (q-1)/2. The original discrete-logarithm problem then splits into 16 separate 48-bit DLPs modulo p and 16 separate 48-bit DLPs modulo q.

What [15] reports is that a 448-ALU NVIDIA Tesla M2050 graphics card takes an average of 23 seconds for these 32 discrete-logarithm computations, i.e., 0.72 seconds for each 48-bit discrete-logarithm computation. The discrete-logarithm computations in [15] use standard techniques, using more than 2²⁴ modular multiplications; the main accomplishment of [15] is at a lower level, using the graphics card to compute 52 million 768-bit modular multiplications per second.

The Tesla M2050 card is currently advertised for $1300. We do not own one; instead we are using a single core of a 6-core 3.3GHz AMD Phenom II X6 1100T CPU. This CPU is no longer available but it cost only $190 when we purchased it last year.

We generated an integer p as 1+2ℓ₁ℓ₂⋯ℓ₁₆, where ℓ₁,ℓ₂,...,ℓ₁₆ are random primes between 2⁴⁸-2²⁰ and 2⁴⁸. We repeated this process until p was prime, and then took ℓ = ℓ₁. This ℓ turned out to be 2⁴⁸-313487. We do not claim that this narrow range of 48-bit primes is cryptographically secure in the context of [15]; we stayed very close to 2⁴⁸ to avoid any possibility of our order-ℓ DLP being noticeably smaller than the DLP in [15]. We chose g as 2⁽ᵖ⁻¹⁾/ℓ in Fₚ*.

For modular multiplications we used the standard C++ interface to the well-known GMP library (version 5.0.2). This interface allows writing readable code such as

```cpp
x = (a * b) % p
```

which turns out to run slightly faster than 1.4 million modular multiplications per second on our single CPU core for our 769-bit p. This understates GMP's internal speeds — it is clear from other benchmarks that we could gain at least a factor of 2 by precomputing an approximate reciprocal of p — but for our experiments we decided to use GMP in the most straightforward way.

We selected T = 64 and W = 1048576; here α = 1/2, i.e., W ≈ (1/2)(ℓ/T)¹ᐟ². Precomputing T table entries used a total of 80289876 ≈ 1.20TW ≈ 0.60(ℓT)¹ᐟ² multiplications; evidently some distinguished points were found more than once. We then carried out a series of 1024 discrete-logarithm experiments, all targeting the same h. Each experiment chose a random y and started a walk from hgʸ, hoping that (1) the walk would reach a distinguished point within 8W steps and (2) the distinguished point would be in the table. If both conditions were satisfied, the experiment double-checked that it had correctly computed the discrete logarithm of h, and finally declared success.

These experiments used a total of 1040325443 ≈ 0.97·1024W multiplications (not counting the occasional multiplications for the randomization of hgʸ and for the double-checks) and succeeded 192 times, on average using 5418361 ≈ 2.58(ℓ/T)¹ᐟ² multiplications per discrete-logarithm computation. Note that the randomization of hgʸ made these speeds independent of h.

### More useful distinguished points
We then changed the precomputation, preserving T = 64 and W = 1048576 but selecting the T table entries as the most useful 64 table entries from a pool of N = 128 distinguished points. This increased the precomputation cost to 167040079 ≈ 1.24NW ≈ 1.24(ℓT)¹ᐟ² multiplications. We ran 4096 new discrete-logarithm experiments, using a total of 3980431381 ≈ 0.93·4096W multiplications and succeeding 1060 times, on average using 3755123 ≈ 1.79(ℓ/T)¹ᐟ² multiplications per discrete-logarithm computation.

### The T¹ᐟ² scaling
We then reduced W to 262144, increased T to 1024, and increased N to 2048. This increased the precomputation cost to 626755730 ≈ 1.17NW ≈ 1.17(ℓT)¹ᐟ² multiplications. We then ran 8192 new experiments, using a total of 2123483139 ≈ 0.99·8192W multiplications and succeeding 2265 times, on average using just 937520 ≈ 1.79(ℓ/T)¹ᐟ² multiplications per discrete-logarithm computation. As predicted the increase of T by a factor of 16 decreased the number of steps by a factor of 4.

We also checked that these computations were running at more than 1.4 million multiplications per second, i.e., under 0.67 seconds per discrete-logarithm computation — less real time on a single CPU core than [15] needed on an entire GPU. There was no noticeable overhead beyond GMP's modular multiplications. The precomputation for T = 1024 took several minutes, but this is not a serious problem for a cryptographic protocol that is going to be run many times.

We then reduced W to 32768, increased T to 65536, and increased N to 131072. This increased the precomputation cost to 5333245354 ≈ 1.24NW ≈ 1.24(ℓT)¹ᐟ² multiplications, roughly an hour. We then ran 4194304 experiments, using a total of 137426510228 ≈ 1.00·4194304W multiplications and succeeding 1187484 times, on average using just 115729 ≈ 1.77(ℓ/T)¹ᐟ² multiplications per discrete-logarithm computation — under 0.1 seconds.

### Optimizing α.
We then carried out a series of experiments with W = 524288, varying both T and N/T as shown in Table 4.1. Each table entry is rounded to 6 digits. The smallest "main computation" table entry, 1.38314 for T = 512 and N/T = 8, means (modulo this rounding) that a series of discrete-logarithm experiments used 1.38314(ℓ/T)¹ᐟ² multiplications per successful discrete-logarithm computation. Each table entry involved 2²⁰ discrete-logarithm experiments, of which more than 2¹⁸ were successful, so each table entry is very likely to have an experimental error below 0.02.

**Table 4.1**: Observed cost for 15 types of discrete-logarithm computations in a group of order ℓ ≈ 2⁴⁸. Each discrete-logarithm experiment used T table entries selected from N distinguished points, and used W = 524288 ≈ α(ℓ/T)¹ᐟ². Each "main computation" table entry reports, for a series of 2²⁰ discrete-logarithm experiments, the average number of multiplications per successful discrete-logarithm computation, scaled by (ℓ/T)¹ᐟ². Each "precomputation" table entry reports the total number of multiplications to build the table, scaled by (ℓT)¹ᐟ².

| T     | 512    | 640    | 768    | 896    | 1024   |
|-------|--------|--------|--------|--------|--------|
| α     | 0.70711| 0.79057| 0.86603| 0.93541| 1.00000|
| precomputation, N=T | 0.84506 | 0.94916 | 1.11884 | 1.23070 | 1.34187 |
| precomputation, N=2T | 1.89769 | 2.33819 | 2.74627 | 3.27589 | 3.66113 |
| precomputation, N=8T | 15.7167 | 20.7087 | 26.1621 | 31.2112 | 36.9350 |
| main computation, N=T | 2.13856 | 2.03391 | 2.01172 | 1.98725 | 2.01289 |
| main computation, N=2T | 1.62474 | 1.59358 | 1.58893 | 1.59218 | 1.61922 |
| main computation, N=8T | 1.38323 | 1.40706 | 1.42941 | 1.46610 | 1.49688 |

This table shows that the optimal choice of α depends on the ratio N/T, but also that rather large variations in α around the optimum make a relatively small difference in performance. Performance is much more heavily affected by increased N/T, i.e., by extra precomputation.

To better understand the tradeoffs between precomputation time and main-computation time, we plotted the 15 pairs of numbers in Table 4.1, obtaining Figure 3. For example, Table 4.1 indicates for T = 512 and N = 2T that each successful discrete-logarithm computation took 1.62474(ℓ/T)¹ᐟ² multiplications on average after 1.89769(ℓT)¹ᐟ² multiplications in the precomputation, so (1.89769, 1.62474) is one of the points plotted in Figure 3. Figure 3 suggests that optimizing α to minimize main-computation time for fixed N/T does not produce the best tradeoff between main-computation time and precomputation time; one should instead decrease α somewhat and increase N/T. To verify this theory we are performing more computations to fill in more points in Figure 3.

### Small-interval experiments.
Starting from the same software, we then made the following tweaks to compute discrete logarithms in a short interval inside a much larger prime-order group:

- We replaced p by a "strong" 256-bit prime, i.e., a prime for which (p-1)/2 is also prime. Of course, 256 bits is not adequate for cryptographic security for groups of the form Fₚ*, but it is adequate for these experiments.
- We replaced g by a large square modulo p.
- We replaced ℓ by exactly 2⁴⁸, and removed the reductions of discrete logarithms modulo ℓ.
- We increased r, the number of precomputed steps, from 32 to 128.
- We generated each step as gʸ with y chosen uniformly at random between 0 and ℓ/(4W), rather than between 0 and ℓ.
- We started each walk from hgʸ with y chosen uniformly at random between 0 and ℓ/2⁸, rather than between 0 and ℓ.
- After each successful experiment, we generated a new target h for the following experiments.

For W = 131072, T = 4096, and N = 8192 the precomputation cost was 1337520628 ≈ 1.25NW ≈ 1.25(ℓT)¹ᐟ² multiplications. We ran 8388608 experiments, using a total of 1100185139821 ≈ 1.00·8388608W multiplications and succeeding 2195416 times, on average using 501128 ≈ 1.91(ℓ/T)¹ᐟ² multiplications per discrete-logarithm computation.

For W = 32768, T = 65536, and N = 131072 the precomputation cost was 5214755468 ≈ 1.21NW ≈ 1.21(ℓT)¹ᐟ² multiplications. We ran 33554432 experiments, using a total of 1097731367293 ≈ 1.00·33554432W multiplications and succeeding 8658974 times, on average using just 126773 ≈ 1.93(ℓ/T)¹ᐟ² multiplications per discrete-logarithm computation.

**Table 4.2**: Observed cost for 15 types of discrete-logarithm computations in an interval of length ℓ = 2⁴⁸ inside a much larger group. Table entries have the same meaning as in Table 4.1.

| T     | 512    | 640    | 768    | 896    | 1024   |
|-------|--------|--------|--------|--------|--------|
| α     | 0.70711| 0.79057| 0.86603| 0.93541| 1.00000|
| precomputation, N=T | 0.85702 | 1.00463 | 1.14077 | 1.28112 | 1.41167 |
| precomputation, N=2T | 1.99640 | 2.38469 | 2.81441 | 3.17253 | 3.61816 |
| precomputation, N=8T | 15.5307 | 20.2547 | 25.2022 | 30.7112 | 36.7452 |
| main computation, N=T | 2.32320 | 2.21685 | 2.14892 | 2.10155 | 2.09915 |
| main computation, N=2T | 1.66106 | 1.64183 | 1.63488 | 1.65603 | 1.66895 |
| main computation, N=8T | 1.44377 | 1.44808 | 1.46581 | 1.49548 | 1.52502 |

Comparing Table 4.2 to Table 4.1 shows that this approach to computing discrete logarithms in an interval of length ℓ uses — for the same table size, and essentially the same amount of precomputation— only slightly more multiplications than computing discrete logarithms in a group of order ℓ.

---

以下是论文第5节和参考文献部分的完整内容，严格保持原文格式与符号：

---

## 5. Space optimization

Each table entry described in Section 3 consists of a group element, at least lg ℓ bits, and a discrete logarithm, also lg ℓ bits, for a total of at least 2T lg ℓ bits. This section explains several ways to compress the table to a much smaller number of bits.

Many of these compression mechanisms slightly increase the number of multiplications used to compute log₉ h. This produces a slightly worse tradeoff between the number of multiplications and the number of table *entries*, but produces a much better tradeoff between the number of multiplications and the number of table *bits*.

For comparison, [22, Table 2] took T = 586463 and W = 2¹¹ for a group of size ℓ ≈ 2⁴², and reported about 2(ℓ/T)¹ᐟ² multiplications per discrete-logarithm computation, using 150 megabytes for the table. Previous sections of this paper explain how to use significantly fewer multiplications for the same T; this section reduces the space consumption by two orders of magnitude for the same T, with only a small increase in the number of multiplications. Equivalently, for the same number of table bits, we use an order of magnitude fewer multiplications.

### Lossless compression of each distinguished point.
There are several standard techniques to reversibly compress elements of commonly used groups. For example, nonzero elements of the "Curve25519" elliptic-curve group are pairs (x,y) ∈ F_q × F_q satisfying y² = x³ + 486662x² + x; here q = 2²⁵⁵ - 19 and ℓ ≈ 2²⁵². This pair is trivially compressed to x and a single bit of y, for a total of 256 ≈ lg ℓ bits.

A typical distinguished-point definition states that a point is distinguished if and only if its bottom lg W bits are 0. These lg W bits need not be stored. This reduces the space for a distinguished elliptic-curve point to approximately lg(ℓ/W) bits; e.g., (2/3) lg ℓ bits for W ≈ ℓ¹ᐟ³.

The other techniques discussed in this section work for any group, not just an elliptic-curve group.

### Replacing each distinguished point with a hash.
To do better we simply suppress some additional bits: we hash each distinguished point to a smaller number of bits and store the hash instead of the distinguished point. This creates a risk of false alarms, but the cost of false alarms is merely the cost of checking a bad guess for log₉ h. Checking one guess takes only about (1 + 1/lg lg ℓ) lg ℓ multiplications, and standard multiexponentiation techniques check several guesses even more efficiently.

If each distinguished point is hashed to lg(T/γ) bits then one expects many false alarms as γ increases past 1. Specifically, a distinguished point outside the table has probability γ/T of colliding with any particular table entry (if the hash behaves randomly), so it is expected to collide with γ table entries overall, creating γ bad guesses for log₉ h. For a successful walk, the expected number of bad guesses drops approximately in half, or slightly below half if the discrete logarithms with each hash are sorted in decreasing order of utility.

If γ is far below W/lg ℓ then the cost of checking γ bad guesses is far below W multiplications, the average cost of a walk. For example, if T is much smaller than W then one can afford to hash each distinguished point to 0 bits: the table then consists simply of T discrete logarithms, occupying T lg ℓ bits, and one checks the end of each walk against each table entry.

### Compressing a sorted sequence of hashes.
It is well known that a sorted sequence of n d-bit integers contains far fewer than nd bits of information when n and d are not very small. "Delta compression" takes advantage of this by computing differences between successive integers and using a variable-length encoding of the differences. For random integers the average difference is close to 2ᵈ/n and is encoded as slightly more than d - lg n bits if d ≥ lg n, saving nearly n lg n bits.

Delta compression does not allow fast random access: to search for an integer one must read the sequence from the beginning. This is not visible in this paper's multiplication counts, but it nevertheless becomes a bottleneck as T grows past W. We instead use a simpler approach that allows fast random access: namely, store the sorted sequence x₁, x₂, ..., xₙ of d-bit integers as
- the sorted sequence x₁, x₂, ..., xₘ of (d-1)-bit integers where m is the largest index such that xₘ < 2ᵈ⁻¹; and
- the sorted sequence xₘ₊₁ - 2ᵈ⁻¹, xₘ₊₂ - 2ᵈ⁻¹, ..., xₙ - 2ᵈ⁻¹ of (d-1)-bit integers.

To search for an integer s we search x₁, ..., xₘ for s if s < 2ᵈ⁻¹ and search xₘ₊₁ - 2ᵈ⁻¹, ..., xₙ - 2ᵈ⁻¹ for s - 2ᵈ⁻¹ if s ≥ 2ᵈ⁻¹. The second search requires a pointer to the second sorted sequence, i.e., a count of the number of bits used to encode x₁, x₂, ..., xₘ.

This transformation saves 1 bit in each of the n table entries at the expense of a small amount of overhead. This is a sensible transformation if the overhead is below n bits. The transformation is inapplicable if d = 0; we encode a sequence of 0-bit integers as simply the number of integers.

Of course, we can and do apply the transformation recursively. The recursion continues for nearly lg n levels if d ≥ lg n, again saving nearly n lg n bits. For small d the compressed sequence drops to a fraction of n bits.

For example, if each distinguished point is hashed to d ≈ lg(4T) bits, at the expense of 1/4 bad guesses for each walk, then the hashes are compressed from Td ≈ T lg(4T) bits to just a few bits per table entry. If each distinguished point is hashed to slightly fewer bits, at the expense of more bad guesses for each walk, then the T hashes are compressed to fewer than T bits; in this case one should concatenate the hashes with the discrete logarithms before applying this compression mechanism.

### Compressing each discrete logarithm.
We finish by considering two mechanisms for compressing discrete logarithms in the table. The first mechanism was introduced in the ongoing ECC2K-130 computation; see [3]. The second mechanism appears to be new.

The first mechanism is as follows. Instead of choosing a random y and starting a walk at gʸ, choose a pseudorandom y determined by a short seed. The seed is about lg T bits, or slightly more if one tries more than T walks; for example, the seed is about 3 times shorter than the discrete logarithm if T ≈ ℓ¹ᐟ³. Store the seed as a proxy for the discrete logarithm of the resulting distinguished point. Reconstructing the discrete logarithm then takes about W multiplications to recompute the walk starting from gʸ. This reconstruction is a bottleneck if distinguished points are hashed to fewer than lg T bits (creating many bad guesses), and it slows down the main computation by a factor of almost 2 if α is large, but if distinguished points are hashed to more than lg T bits and α is small then the reconstruction cost is outweighed by the space savings.

The second mechanism is to simply suppress most of the bits of the discrete logarithm. Reconstructing those bits is then a discrete-logarithm problem in a smaller interval; solve these reconstruction problems with the same algorithm recursively, using a smaller table and a smaller number of multiplications. For example, communicating just 9 bits of an ℓ-bit discrete logarithm means reducing an ℓ-bit DLP to an (ℓ-9)-bit DLP, which takes 1/8th as many multiplications using a T/8-entry table (or 1/16th as many multiplications using a T/2-entry table); if the number of bad guesses is sufficiently small then this is a good tradeoff.

Note that this second mechanism relies on being able to quickly compute discrete logarithms in small intervals, even if the original goal is to compute discrete logarithms in small groups.

---

## References

[1] — (no editor), *2nd ACM conference on computer and communication security, Fairfax, Virginia, November 1994*, Association for Computing Machinery, 1994. See [34].

[2] Mikhail J. Atallah, Nicholas J. Hopper (editors), *Privacy enhancing technologies, 10th international symposium, PETS 2010, Berlin, Germany, July 21-23, 2010, proceedings*, Lecture Notes in Computer Science, 6205, Springer, 2010. ISBN 978-3-642-14526-1. See [16].

[3] Daniel V. Bailey, Lejla Batina, Daniel J. Bernstein, Peter Birkner, Joppe W. Bos, Hsieh-Chung Chen, Chen-Mou Cheng, Gauthier Van Damme, Giacomo de Meulenaer, Luis Julian Dominguez Perez, Junfeng Fan, Tim Guneysu, Frank Gurkaynak, Thorsten Kleinjung, Tanja Lange, Nele Mentens, Ruben Niederhagen, Christof Paar, Francesco Regazzoni, Peter Schwabe, Leif Uhsadel, Anthony Van Herrewege, Bo-Yin Yang, *Breaking ECC2K-130* (2010). URL: http://eprint.iacr.org/2009/541/. Citations in this document: §5.

[...] *[Remaining references 4-36 follow the same exact formatting as in the original paper, listing all authors, titles, publication venues, and URLs/DOIs where applicable.]*

---