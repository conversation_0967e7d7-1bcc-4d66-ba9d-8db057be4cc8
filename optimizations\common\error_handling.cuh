/**
 * @file error_handling.cuh
 * @brief 统一的错误处理和内存管理 - 修复BUG
 * 
 * 提供统一的CUDA错误处理和内存管理功能
 * 修复项目中发现的内存泄漏和错误处理问题
 */

#ifndef ERROR_HANDLING_CUH
#define ERROR_HANDLING_CUH

#include <cuda_runtime.h>
#include <cstdio>
#include <cstdlib>
#include <memory>

/**
 * @brief CUDA错误检查宏 - 统一错误处理
 */
#define CUDA_CHECK_UNIFIED(call) do { \
    cudaError_t err = call; \
    if (err != cudaSuccess) { \
        printf("[CUDA ERROR] %s:%d: %s\n", __FILE__, __LINE__, cudaGetErrorString(err)); \
        return false; \
    } \
} while(0)

#define CUDA_CHECK_VOID_UNIFIED(call) do { \
    cudaError_t err = call; \
    if (err != cudaSuccess) { \
        printf("[CUDA ERROR] %s:%d: %s\n", __FILE__, __LINE__, cudaGetErrorString(err)); \
        return; \
    } \
} while(0)

#define CUDA_CHECK_PTR_UNIFIED(call) do { \
    cudaError_t err = call; \
    if (err != cudaSuccess) { \
        printf("[CUDA ERROR] %s:%d: %s\n", __FILE__, __LINE__, cudaGetErrorString(err)); \
        return nullptr; \
    } \
} while(0)

/**
 * @brief 安全的CUDA内存分配器
 */
class SafeCudaAllocator {
private:
    struct AllocationInfo {
        void* ptr;
        size_t size;
        const char* file;
        int line;
        
        AllocationInfo(void* p, size_t s, const char* f, int l) 
            : ptr(p), size(s), file(f), line(l) {}
    };
    
    static std::vector<AllocationInfo> allocations;
    static size_t total_allocated;
    
public:
    /**
     * @brief 安全的CUDA内存分配
     */
    static void* allocate(size_t size, const char* file, int line) {
        void* ptr = nullptr;
        cudaError_t err = cudaMalloc(&ptr, size);
        
        if (err != cudaSuccess) {
            printf("[CUDA ALLOC ERROR] %s:%d: Failed to allocate %zu bytes: %s\n", 
                   file, line, size, cudaGetErrorString(err));
            return nullptr;
        }
        
        allocations.emplace_back(ptr, size, file, line);
        total_allocated += size;
        
        printf("[CUDA ALLOC] %s:%d: Allocated %zu bytes at %p (total: %zu bytes)\n", 
               file, line, size, ptr, total_allocated);
        
        return ptr;
    }
    
    /**
     * @brief 安全的CUDA内存释放
     */
    static bool deallocate(void* ptr, const char* file, int line) {
        if (!ptr) return true;
        
        // 查找分配记录
        auto it = std::find_if(allocations.begin(), allocations.end(),
                              [ptr](const AllocationInfo& info) { return info.ptr == ptr; });
        
        if (it == allocations.end()) {
            printf("[CUDA FREE WARNING] %s:%d: Attempting to free untracked pointer %p\n", 
                   file, line, ptr);
        } else {
            total_allocated -= it->size;
            printf("[CUDA FREE] %s:%d: Freed %zu bytes at %p (total: %zu bytes)\n", 
                   file, line, it->size, ptr, total_allocated);
            allocations.erase(it);
        }
        
        cudaError_t err = cudaFree(ptr);
        if (err != cudaSuccess) {
            printf("[CUDA FREE ERROR] %s:%d: Failed to free pointer %p: %s\n", 
                   file, line, ptr, cudaGetErrorString(err));
            return false;
        }
        
        return true;
    }
    
    /**
     * @brief 检查内存泄漏
     */
    static void check_leaks() {
        if (!allocations.empty()) {
            printf("[MEMORY LEAK WARNING] %zu unfreed allocations detected:\n", allocations.size());
            for (const auto& info : allocations) {
                printf("  - %zu bytes at %p (allocated at %s:%d)\n", 
                       info.size, info.ptr, info.file, info.line);
            }
        } else {
            printf("[MEMORY CHECK] No memory leaks detected\n");
        }
    }
    
    /**
     * @brief 强制清理所有分配
     */
    static void cleanup_all() {
        printf("[MEMORY CLEANUP] Cleaning up %zu allocations\n", allocations.size());
        for (const auto& info : allocations) {
            cudaFree(info.ptr);
            printf("  - Freed %zu bytes at %p\n", info.size, info.ptr);
        }
        allocations.clear();
        total_allocated = 0;
    }
};

// 静态成员初始化
std::vector<SafeCudaAllocator::AllocationInfo> SafeCudaAllocator::allocations;
size_t SafeCudaAllocator::total_allocated = 0;

/**
 * @brief 安全分配宏
 */
#define SAFE_CUDA_MALLOC(size) SafeCudaAllocator::allocate(size, __FILE__, __LINE__)
#define SAFE_CUDA_FREE(ptr) SafeCudaAllocator::deallocate(ptr, __FILE__, __LINE__)

/**
 * @brief RAII CUDA内存管理器
 */
template<typename T>
class CudaMemoryGuard {
private:
    T* ptr_;
    size_t count_;
    
public:
    explicit CudaMemoryGuard(size_t count) : count_(count) {
        ptr_ = static_cast<T*>(SAFE_CUDA_MALLOC(count * sizeof(T)));
        if (!ptr_) {
            throw std::runtime_error("Failed to allocate CUDA memory");
        }
    }
    
    ~CudaMemoryGuard() {
        if (ptr_) {
            SAFE_CUDA_FREE(ptr_);
        }
    }
    
    // 禁止拷贝
    CudaMemoryGuard(const CudaMemoryGuard&) = delete;
    CudaMemoryGuard& operator=(const CudaMemoryGuard&) = delete;
    
    // 支持移动
    CudaMemoryGuard(CudaMemoryGuard&& other) noexcept 
        : ptr_(other.ptr_), count_(other.count_) {
        other.ptr_ = nullptr;
        other.count_ = 0;
    }
    
    CudaMemoryGuard& operator=(CudaMemoryGuard&& other) noexcept {
        if (this != &other) {
            if (ptr_) SAFE_CUDA_FREE(ptr_);
            ptr_ = other.ptr_;
            count_ = other.count_;
            other.ptr_ = nullptr;
            other.count_ = 0;
        }
        return *this;
    }
    
    T* get() const { return ptr_; }
    T* operator->() const { return ptr_; }
    T& operator*() const { return *ptr_; }
    T& operator[](size_t index) const { return ptr_[index]; }
    
    size_t size() const { return count_; }
    bool valid() const { return ptr_ != nullptr; }
    
    /**
     * @brief 释放所有权
     */
    T* release() {
        T* temp = ptr_;
        ptr_ = nullptr;
        count_ = 0;
        return temp;
    }
};

/**
 * @brief 数组边界检查宏
 */
#define BOUNDS_CHECK(index, size) do { \
    if ((index) >= (size)) { \
        printf("[BOUNDS ERROR] %s:%d: Index %zu out of bounds (size: %zu)\n", \
               __FILE__, __LINE__, (size_t)(index), (size_t)(size)); \
        return false; \
    } \
} while(0)

#define BOUNDS_CHECK_VOID(index, size) do { \
    if ((index) >= (size)) { \
        printf("[BOUNDS ERROR] %s:%d: Index %zu out of bounds (size: %zu)\n", \
               __FILE__, __LINE__, (size_t)(index), (size_t)(size)); \
        return; \
    } \
} while(0)

/**
 * @brief 指针有效性检查宏
 */
#define NULL_CHECK(ptr) do { \
    if (!(ptr)) { \
        printf("[NULL POINTER ERROR] %s:%d: Null pointer detected\n", __FILE__, __LINE__); \
        return false; \
    } \
} while(0)

#define NULL_CHECK_VOID(ptr) do { \
    if (!(ptr)) { \
        printf("[NULL POINTER ERROR] %s:%d: Null pointer detected\n", __FILE__, __LINE__); \
        return; \
    } \
} while(0)

/**
 * @brief 内存清理辅助类
 */
class MemoryCleanupHelper {
public:
    static void register_cleanup_handler() {
        std::atexit([]() {
            SafeCudaAllocator::check_leaks();
            SafeCudaAllocator::cleanup_all();
        });
    }
};

#endif // ERROR_HANDLING_CUH
