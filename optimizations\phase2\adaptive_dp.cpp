#include "adaptive_dp.h"
#include "optimizations/phase1/gpu_detector.h"
#include <cuda_runtime.h>
#include <cmath>

#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

AdaptiveDP::AdaptiveDP() 
    : current_dp_bits(20), total_kangaroos(0), range_size_bits(0),
      collision_rate(0.0), total_operations(0), total_collisions(0),
      gpu_major(0), gpu_minor(0), gpu_memory(0),
      dp_found_count(0), last_adjustment_ops(0), target_collision_rate(0.05) {}

AdaptiveDP::~AdaptiveDP() {}

void AdaptiveDP::initializeGPUInfo(int device_id) {
    cudaDeviceProp prop;
    cudaError_t error = cudaGetDeviceProperties(&prop, device_id);
    if (error == cudaSuccess) {
        gpu_major = prop.major;
        gpu_minor = prop.minor;
        gpu_memory = prop.totalGlobalMem;
        
        std::cout << "AdaptiveDP: Initialized for GPU " << prop.name 
                  << " (SM " << gpu_major << "." << gpu_minor << ")" << std::endl;
    } else {
        std::cerr << "AdaptiveDP: Failed to get GPU properties" << std::endl;
        gpu_major = 7;  // 默认值
        gpu_minor = 5;
        gpu_memory = 8ULL * 1024 * 1024 * 1024;  // 8GB默认
    }
}

uint32_t AdaptiveDP::calculateOptimalDP(const Int& range_start, 
                                       const Int& range_end,
                                       uint64_t num_kangaroos) {
    total_kangaroos = num_kangaroos;
    
    // 计算搜索范围位数 (简化实现)
    // 这里使用简化的位数计算，实际应该使用Int类的方法
    range_size_bits = 64;  // 默认64位，实际应该根据range_end-range_start计算
    
    std::cout << "AdaptiveDP: Calculating optimal DP for:" << std::endl;
    std::cout << "  Range: 2^" << range_size_bits << " bits" << std::endl;
    std::cout << "  Kangaroos: " << num_kangaroos << std::endl;
    
    // 计算基础DP位数
    uint32_t base_dp = calculateBaseDPBits(range_size_bits, num_kangaroos);
    
    // 应用GPU架构调整
    uint32_t gpu_adjusted_dp = applyGPUAdjustment(base_dp);
    
    // 应用内存限制
    uint32_t memory_adjusted_dp = applyMemoryConstraints(gpu_adjusted_dp, num_kangaroos);
    
    // 验证合理性
    current_dp_bits = validateDPBits(memory_adjusted_dp);
    
    std::cout << "  Base DP: " << base_dp << " bits" << std::endl;
    std::cout << "  GPU adjusted: " << gpu_adjusted_dp << " bits" << std::endl;
    std::cout << "  Memory adjusted: " << memory_adjusted_dp << " bits" << std::endl;
    std::cout << "  Final DP: " << current_dp_bits << " bits" << std::endl;
    
    return current_dp_bits;
}

uint32_t AdaptiveDP::calculateBaseDPBits(uint64_t range_bits, uint64_t kangaroo_count) const {
    // Pollard's kangaroo理论: 期望步数 = sqrt(π*N/4)
    // 其中N是搜索空间大小
    double search_space_log2 = (double)range_bits;
    double expected_steps_log2 = search_space_log2 / 2.0 + log2(sqrt(M_PI / 4.0));
    
    // DP密度应该使得期望DP数量适中
    // 目标: 每个袋鼠产生合理数量的DP
    double kangaroo_log2 = log2((double)kangaroo_count);
    double dp_density_log2 = expected_steps_log2 - kangaroo_log2 - log2(100.0);  // 每袋鼠约100个DP
    
    uint32_t dp_bits = (uint32_t)std::max(12.0, std::min(32.0, dp_density_log2));
    
    return dp_bits;
}

uint32_t AdaptiveDP::applyGPUAdjustment(uint32_t base_dp_bits) const {
    int adjustment = getGPUArchitectureAdjustment(gpu_major, gpu_minor);
    int adjusted = (int)base_dp_bits + adjustment;
    return (uint32_t)std::max(12, std::min(32, adjusted));
}

int AdaptiveDP::getGPUArchitectureAdjustment(int major, int minor) {
    if (major <= 6) {
        // Pascal及以下: 增加DP位数减少内存压力
        return +2;
    } else if (major == 7) {
        // Volta/Turing: 平衡配置
        return 0;
    } else if (major == 8) {
        // Ampere: 可以处理更多DP
        return -1;
    } else {
        // Ada/Hopper: 激进配置
        return -2;
    }
}

uint32_t AdaptiveDP::applyMemoryConstraints(uint32_t dp_bits, uint64_t kangaroo_count) const {
    size_t estimated_memory = calculateMemoryUsage(dp_bits, kangaroo_count);
    size_t available_memory = gpu_memory * 8 / 10;  // 使用80%的GPU内存
    
    while (estimated_memory > available_memory && dp_bits < 30) {
        dp_bits++;
        estimated_memory = calculateMemoryUsage(dp_bits, kangaroo_count);
    }
    
    return dp_bits;
}

size_t AdaptiveDP::calculateMemoryUsage(uint32_t dp_bits, uint64_t kangaroo_count) {
    // 袋鼠状态内存: 每个袋鼠约100字节
    size_t kangaroo_memory = kangaroo_count * 100;
    
    // 哈希表内存: 基于DP密度估算
    double dp_density = 1.0 / (1ULL << dp_bits);
    uint64_t expected_operations = kangaroo_count * 10000;  // 假设每袋鼠10K步
    uint64_t expected_dp_count = (uint64_t)(expected_operations * dp_density);
    size_t hashtable_memory = expected_dp_count * 64;  // 每个DP项约64字节
    
    return kangaroo_memory + hashtable_memory;
}

uint32_t AdaptiveDP::validateDPBits(uint32_t dp_bits) const {
    return std::max(12u, std::min(32u, dp_bits));
}

bool AdaptiveDP::adjustDP(double current_collision_rate, uint64_t operations) {
    collision_rate = current_collision_rate;
    total_operations = operations;
    
    // 检查是否需要调整
    if (!needsAdjustment()) {
        return false;
    }
    
    uint32_t old_dp_bits = current_dp_bits;
    
    if (collision_rate > target_collision_rate * 1.5) {
        // 碰撞率过高，增加DP位数
        if (current_dp_bits < 30) {
            current_dp_bits++;
            std::cout << "AdaptiveDP: Increased DP bits to " << current_dp_bits 
                      << " (collision rate: " << std::fixed << std::setprecision(4) 
                      << (collision_rate * 100.0) << "%)" << std::endl;
        }
    } else if (collision_rate < target_collision_rate * 0.5 && total_operations > 1000000) {
        // 碰撞率过低，减少DP位数
        if (current_dp_bits > 12) {
            current_dp_bits--;
            std::cout << "AdaptiveDP: Decreased DP bits to " << current_dp_bits 
                      << " (collision rate: " << std::fixed << std::setprecision(4) 
                      << (collision_rate * 100.0) << "%)" << std::endl;
        }
    }
    
    if (current_dp_bits != old_dp_bits) {
        last_adjustment_ops = operations;
        return true;
    }
    
    return false;
}

bool AdaptiveDP::needsAdjustment() const {
    // 至少运行100万次操作后才考虑调整
    if (total_operations < 1000000) return false;
    
    // 距离上次调整至少500万次操作
    if (total_operations - last_adjustment_ops < 5000000) return false;
    
    // 碰撞率偏离目标值超过50%
    if (collision_rate > target_collision_rate * 1.5 || 
        collision_rate < target_collision_rate * 0.5) {
        return true;
    }
    
    return false;
}

uint64_t AdaptiveDP::getDPMask() const {
    return (1ULL << current_dp_bits) - 1;
}

void AdaptiveDP::updateStatistics(uint64_t new_operations, 
                                 uint32_t new_collisions,
                                 uint32_t new_dp_found) {
    total_operations += new_operations;
    total_collisions += new_collisions;
    dp_found_count += new_dp_found;
    
    if (total_operations > 0) {
        collision_rate = (double)total_collisions / (double)total_operations;
    }
}

double AdaptiveDP::calculateExpectedSteps(uint64_t range_bits) {
    return sqrt(M_PI / 4.0) * pow(2.0, (double)range_bits / 2.0);
}

void AdaptiveDP::printDPInfo() const {
    std::cout << "=== Adaptive DP Status ===" << std::endl;
    std::cout << "Current DP bits: " << current_dp_bits << std::endl;
    std::cout << "DP mask: 0x" << std::hex << getDPMask() << std::dec << std::endl;
    std::cout << "Collision rate: " << std::fixed << std::setprecision(4) 
              << (collision_rate * 100.0) << "%" << std::endl;
    std::cout << "Target rate: " << std::fixed << std::setprecision(4) 
              << (target_collision_rate * 100.0) << "%" << std::endl;
    std::cout << "Total operations: " << total_operations << std::endl;
    std::cout << "DP found: " << dp_found_count << std::endl;
}

void AdaptiveDP::printDetailedStatistics() const {
    auto metrics = getPerformanceMetrics();
    
    std::cout << "=== Detailed DP Statistics ===" << std::endl;
    std::cout << "Range: 2^" << range_size_bits << " bits" << std::endl;
    std::cout << "Kangaroos: " << total_kangaroos << std::endl;
    std::cout << "GPU: SM " << gpu_major << "." << gpu_minor << std::endl;
    std::cout << "Memory: " << (gpu_memory / (1024*1024)) << " MB" << std::endl;
    std::cout << std::endl;
    
    std::cout << "Performance Metrics:" << std::endl;
    std::cout << "  Collision rate: " << std::fixed << std::setprecision(4) 
              << (metrics.collision_rate * 100.0) << "%" << std::endl;
    std::cout << "  DP density: " << std::scientific << std::setprecision(2) 
              << metrics.dp_density << std::endl;
    std::cout << "  Memory efficiency: " << std::fixed << std::setprecision(2) 
              << (metrics.memory_efficiency * 100.0) << "%" << std::endl;
    std::cout << "  Theoretical progress: " << std::fixed << std::setprecision(4) 
              << (metrics.theoretical_progress * 100.0) << "%" << std::endl;
    std::cout << "  Operations/sec: " << metrics.operations_per_second << std::endl;
}

AdaptiveDP::PerformanceMetrics AdaptiveDP::getPerformanceMetrics() const {
    PerformanceMetrics metrics;
    
    metrics.collision_rate = collision_rate;
    metrics.dp_density = 1.0 / (1ULL << current_dp_bits);
    
    size_t used_memory = calculateMemoryUsage(current_dp_bits, total_kangaroos);
    metrics.memory_efficiency = (double)used_memory / (double)gpu_memory;
    
    double expected_steps = calculateExpectedSteps(range_size_bits);
    metrics.theoretical_progress = (double)total_operations / expected_steps;
    
    // 简单的操作速度估算 (需要实际测量)
    metrics.operations_per_second = total_operations / 60;  // 假设运行1分钟
    
    return metrics;
}

void AdaptiveDP::resetStatistics() {
    total_operations = 0;
    total_collisions = 0;
    dp_found_count = 0;
    collision_rate = 0.0;
    last_adjustment_ops = 0;
}

void AdaptiveDP::setTargetCollisionRate(double target_rate) {
    target_collision_rate = std::max(0.01, std::min(0.1, target_rate));
}

// DPUtils命名空间实现
namespace DPUtils {
    double calculateDPDensity(uint32_t dp_bits) {
        return 1.0 / (1ULL << dp_bits);
    }
    
    size_t estimateHashTableSize(uint32_t dp_bits, uint64_t expected_operations) {
        double dp_density = calculateDPDensity(dp_bits);
        uint64_t expected_dp_count = (uint64_t)(expected_operations * dp_density);
        return expected_dp_count * 64;  // 每个DP项64字节
    }
    
    double calculateCollisionProbability(uint32_t dp_bits, uint64_t operations) {
        double dp_space = pow(2.0, (double)dp_bits);
        return 1.0 - exp(-((double)operations * operations) / (2.0 * dp_space));
    }
}
