#ifndef PHASE3_CONCEPT_H
#define PHASE3_CONCEPT_H

#include <iostream>
#include <vector>
#include <memory>

/**
 * @file phase3_concept.h
 * @brief Phase 3 内存系统重构 - 概念验证
 * 
 * 这是Phase 3的概念验证实现，展示分片哈希表和GPU内存池的基本思路
 * 为将来的完整实现奠定基础
 */

/**
 * @brief 简化的分片哈希表概念
 */
class Phase3ConceptHashTable {
private:
    std::vector<std::vector<std::pair<uint64_t, uint64_t>>> shards;
    size_t num_shards;
    size_t total_items;
    
public:
    /**
     * @brief 构造函数
     * @param shard_count 分片数量
     */
    Phase3ConceptHashTable(size_t shard_count = 16) 
        : num_shards(shard_count), total_items(0) {
        shards.resize(num_shards);
        std::cout << "Phase3ConceptHashTable: Initialized " << num_shards << " shards" << std::endl;
    }
    
    /**
     * @brief 插入键值对
     * @param key 键
     * @param value 值
     * @return 是否发现碰撞
     */
    bool insert(uint64_t key, uint64_t value) {
        size_t shard_idx = key % num_shards;
        auto& shard = shards[shard_idx];
        
        // 检查是否已存在
        for (const auto& item : shard) {
            if (item.first == key) {
                std::cout << "🎉 Collision detected! Key: 0x" << std::hex << key << std::dec << std::endl;
                return true;
            }
        }
        
        // 插入新项
        shard.emplace_back(key, value);
        total_items++;
        return false;
    }
    
    /**
     * @brief 查找键
     * @param key 键
     * @param value 输出值
     * @return 是否找到
     */
    bool find(uint64_t key, uint64_t& value) {
        size_t shard_idx = key % num_shards;
        const auto& shard = shards[shard_idx];
        
        for (const auto& item : shard) {
            if (item.first == key) {
                value = item.second;
                return true;
            }
        }
        return false;
    }
    
    /**
     * @brief 获取统计信息
     */
    void printStatistics() const {
        std::cout << "=== Phase 3 Concept Hash Table Statistics ===" << std::endl;
        std::cout << "Total shards: " << num_shards << std::endl;
        std::cout << "Total items: " << total_items << std::endl;
        std::cout << "Average items per shard: " << (double)total_items / num_shards << std::endl;
        
        // 负载均衡统计
        size_t min_size = SIZE_MAX, max_size = 0;
        for (const auto& shard : shards) {
            min_size = std::min(min_size, shard.size());
            max_size = std::max(max_size, shard.size());
        }
        
        double load_balance = (max_size > 0) ? (1.0 - (double)(max_size - min_size) / max_size) : 1.0;
        std::cout << "Load balance: " << (load_balance * 100.0) << "%" << std::endl;
    }
    
    /**
     * @brief 清空所有数据
     */
    void clear() {
        for (auto& shard : shards) {
            shard.clear();
        }
        total_items = 0;
    }
};

/**
 * @brief 简化的GPU内存池概念
 */
class Phase3ConceptMemoryPool {
private:
    struct MemoryBlock {
        void* ptr;
        size_t size;
        bool in_use;
        
        MemoryBlock(void* p, size_t s) : ptr(p), size(s), in_use(true) {}
    };
    
    std::vector<MemoryBlock> blocks;
    size_t total_allocated;
    size_t total_used;
    
public:
    /**
     * @brief 构造函数
     */
    Phase3ConceptMemoryPool() : total_allocated(0), total_used(0) {
        std::cout << "Phase3ConceptMemoryPool: Initialized" << std::endl;
    }
    
    /**
     * @brief 析构函数
     */
    ~Phase3ConceptMemoryPool() {
        cleanup();
    }
    
    /**
     * @brief 模拟内存分配
     * @param size 内存大小
     * @return 内存指针
     */
    void* allocate(size_t size) {
        // 查找空闲块
        for (auto& block : blocks) {
            if (!block.in_use && block.size >= size) {
                block.in_use = true;
                total_used += block.size;
                return block.ptr;
            }
        }
        
        // 创建新块 (模拟)
        void* ptr = malloc(size);  // 使用malloc模拟GPU内存
        if (ptr) {
            blocks.emplace_back(ptr, size);
            total_allocated += size;
            total_used += size;
        }
        
        return ptr;
    }
    
    /**
     * @brief 模拟内存释放
     * @param ptr 内存指针
     */
    void deallocate(void* ptr) {
        for (auto& block : blocks) {
            if (block.ptr == ptr && block.in_use) {
                block.in_use = false;
                total_used -= block.size;
                return;
            }
        }
    }
    
    /**
     * @brief 获取统计信息
     */
    void printStatistics() const {
        std::cout << "=== Phase 3 Concept Memory Pool Statistics ===" << std::endl;
        std::cout << "Total allocated: " << (total_allocated / 1024) << " KB" << std::endl;
        std::cout << "Currently used: " << (total_used / 1024) << " KB" << std::endl;
        std::cout << "Total blocks: " << blocks.size() << std::endl;
        
        size_t free_blocks = 0;
        for (const auto& block : blocks) {
            if (!block.in_use) free_blocks++;
        }
        std::cout << "Free blocks: " << free_blocks << std::endl;
        
        double utilization = (total_allocated > 0) ? (double)total_used / total_allocated : 0.0;
        std::cout << "Utilization: " << (utilization * 100.0) << "%" << std::endl;
    }
    
    /**
     * @brief 清理所有内存
     */
    void cleanup() {
        for (const auto& block : blocks) {
            free(block.ptr);  // 释放模拟的内存
        }
        blocks.clear();
        total_allocated = 0;
        total_used = 0;
    }
};

/**
 * @brief Phase 3 概念验证演示
 */
class Phase3ConceptDemo {
public:
    /**
     * @brief 运行概念验证演示
     */
    static void runDemo() {
        std::cout << "\n=== Phase 3 Concept Verification Demo ===" << std::endl;
        
        // 测试分片哈希表
        std::cout << "\n--- Testing Sharded Hash Table ---" << std::endl;
        Phase3ConceptHashTable hashTable(8);
        
        // 插入一些测试数据
        for (uint64_t i = 0; i < 100; i++) {
            uint64_t key = i * 12345 + 67890;
            uint64_t value = i * 98765 + 43210;
            
            if (hashTable.insert(key, value)) {
                std::cout << "Found collision at iteration " << i << std::endl;
                break;
            }
        }
        
        hashTable.printStatistics();
        
        // 测试GPU内存池
        std::cout << "\n--- Testing GPU Memory Pool ---" << std::endl;
        Phase3ConceptMemoryPool memPool;
        
        // 分配一些内存块
        std::vector<void*> ptrs;
        for (int i = 0; i < 10; i++) {
            size_t size = (i + 1) * 1024;  // 1KB, 2KB, 3KB, ...
            void* ptr = memPool.allocate(size);
            if (ptr) {
                ptrs.push_back(ptr);
            }
        }
        
        memPool.printStatistics();
        
        // 释放一些内存
        for (size_t i = 0; i < ptrs.size(); i += 2) {
            memPool.deallocate(ptrs[i]);
        }
        
        std::cout << "\nAfter deallocating every other block:" << std::endl;
        memPool.printStatistics();
        
        std::cout << "\n=== Phase 3 Concept Demo Complete ===" << std::endl;
    }
};

#endif // PHASE3_CONCEPT_H
