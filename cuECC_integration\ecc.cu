#include "curve/secp256k1.cuh"
#include "ecc.cuh"
#include <cuda.h>
#include <cuda_runtime_api.h>
#include <stdio.h>

#define cudaRunOrAbort(ans)                                                    \
  { cudaAssert((ans), __FILE__, __LINE__); }
inline void cudaAssert(cudaError_t code, const char *file, int line,
                       bool abort = true) {
  if (code != cudaSuccess) {
    fprintf(stderr, "[CUDA] Error: %s (%s:%d)\n", cudaGetErrorString(code),
            file, line);
    if (abort)
      exit(code);
  }
}

__global__ void getPublicKeyByPrivateKeyKernel(cuECC_Point_Internal *output, u64 *privateKey,
                                               int n) {
  int i = blockIdx.x * blockDim.x + threadIdx.x;

  if (i < n) {
    secp256k1PublicKey(output + i, privateKey + i * 4);
  }
}

// 🔧 适配器函数实现
__device__ inline void cuECC_secp256k1_ecmult_gen(cuECC_Point *result, const u64 *scalar) {
    secp256k1PublicKey(result, scalar);
}

// 用于测试的简单内核实现
__global__ void test_cuECC_ecmult_gen(cuECC_Point *results, const u64 *scalars, int count) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx < count) {
        cuECC_secp256k1_ecmult_gen(&results[idx], &scalars[idx * 4]);
    }
}

extern "C" void getPublicKeyByPrivateKey(cuECC_Point_Internal output[],
                                         u64 flattenedPrivateKeys[][4], int n) {
  dim3 blockDim(256);
  dim3 gridDim((n + blockDim.x - 1) / blockDim.x);

  int numberOfStreams = gridDim.x;

  // 🔧 修复变长数组问题：使用动态分配
  cudaStream_t *streams = new cudaStream_t[numberOfStreams];

  cuECC_Point_Internal *pinnedPoints;
  cuECC_Point_Internal *devicePoints;
  u64 *pinnedPrivateKeys;
  u64 *devicePrivateKeys;

  cudaRunOrAbort(cudaMallocHost(&pinnedPoints, n * sizeof(cuECC_Point_Internal)));
  cudaRunOrAbort(cudaMallocHost(&pinnedPrivateKeys, n * 4 * sizeof(u64)));
  cudaRunOrAbort(cudaMalloc(&devicePoints, n * sizeof(cuECC_Point_Internal)));
  cudaRunOrAbort(cudaMalloc(&devicePrivateKeys, n * 4 * sizeof(u64)));

  for (int i = 0; i < n; i++) {
    for (int j = 0; j < 4; j++) {
      pinnedPrivateKeys[i * 4 + j] = flattenedPrivateKeys[i][j];
    }
  }

  for (int i = 0; i < numberOfStreams; i++) {
    cudaRunOrAbort(cudaStreamCreate(&streams[i]));

    int dimension = (i == numberOfStreams - 1) ? dimension = n - i * blockDim.x
                                               : blockDim.x;

    cudaRunOrAbort(cudaMemcpyAsync(devicePrivateKeys + i * blockDim.x * 4,
                                   pinnedPrivateKeys + i * blockDim.x * 4,
                                   dimension * 4 * sizeof(u64),
                                   cudaMemcpyHostToDevice, streams[i]));

    getPublicKeyByPrivateKeyKernel<<<1, blockDim, 0, streams[i]>>>(
        devicePoints + i * blockDim.x, devicePrivateKeys + i * blockDim.x * 4,
        dimension);

    cudaRunOrAbort(cudaMemcpyAsync(
        pinnedPoints + i * blockDim.x, devicePoints + i * blockDim.x,
        dimension * sizeof(cuECC_Point_Internal), cudaMemcpyDeviceToHost, streams[i]));
  }

  for (int i = 0; i < numberOfStreams; i++) {
    cudaRunOrAbort(cudaStreamSynchronize(streams[i]));
    cudaRunOrAbort(cudaStreamDestroy(streams[i]));
  }

  for (int i = 0; i < n; i++) {
    output[i] = pinnedPoints[i];
  }

  cudaRunOrAbort(cudaFreeHost(pinnedPoints));
  cudaRunOrAbort(cudaFreeHost(pinnedPrivateKeys));
  cudaRunOrAbort(cudaFree(devicePoints));
  cudaRunOrAbort(cudaFree(devicePrivateKeys));

  // 🔧 释放动态分配的streams数组
  delete[] streams;
}
