^D:\MYBITCOIN\2\CUDA-BSGS-PRODUCTION-FULL\KANGAROO\CMAKELISTS.TXT
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/mybitcoin/2/cuda-bsgs-production-full/Kangaroo -BD:/mybitcoin/2/cuda-bsgs-production-full/Kangaroo/build --check-stamp-file D:/mybitcoin/2/cuda-bsgs-production-full/Kangaroo/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
