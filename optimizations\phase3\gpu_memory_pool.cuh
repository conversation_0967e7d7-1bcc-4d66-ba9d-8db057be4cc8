/**
 * @file gpu_memory_pool.cuh
 * @brief GPU内存池系统 - Phase 3内存优化
 * 
 * 实现高效的GPU内存池，减少内存分配开销
 * 支持512-bit数据和大范围搜索优化
 */

#ifndef GPU_MEMORY_POOL_CUH
#define GPU_MEMORY_POOL_CUH

#include <cuda_runtime.h>
#include <cstdint>
#include <vector>
#include <mutex>

/**
 * @brief 内存块描述符
 */
struct MemoryBlock {
    void *ptr;              // 内存指针
    size_t size;            // 块大小
    bool is_free;           // 是否空闲
    uint64_t allocation_id; // 分配ID
    
    MemoryBlock() : ptr(nullptr), size(0), is_free(true), allocation_id(0) {}
    MemoryBlock(void *p, size_t s) : ptr(p), size(s), is_free(false), allocation_id(0) {}
};

/**
 * @brief 内存池配置
 */
struct MemoryPoolConfig {
    static const size_t DEFAULT_POOL_SIZE = 512 * 1024 * 1024;  // 512MB默认池大小
    static const size_t MIN_BLOCK_SIZE = 1024;                  // 最小块大小1KB
    static const size_t MAX_BLOCK_SIZE = 64 * 1024 * 1024;      // 最大块大小64MB
    static const int MAX_POOLS = 8;                             // 最大池数量
    static const size_t ALIGNMENT = 256;                        // 内存对齐256字节
};

/**
 * @brief GPU内存池类
 */
class GPUMemoryPool {
private:
    struct Pool {
        void *base_ptr;                    // 池基地址
        size_t total_size;                 // 池总大小
        size_t used_size;                  // 已使用大小
        std::vector<MemoryBlock> blocks;   // 内存块列表
        std::mutex mutex;                  // 线程安全锁
        
        Pool() : base_ptr(nullptr), total_size(0), used_size(0) {}
    };
    
    std::vector<Pool> pools;               // 内存池列表
    uint64_t next_allocation_id;           // 下一个分配ID
    std::mutex global_mutex;               // 全局锁
    
    // 统计信息
    size_t total_allocated;                // 总分配内存
    size_t peak_usage;                     // 峰值使用量
    uint64_t allocation_count;             // 分配次数
    uint64_t deallocation_count;           // 释放次数
    
public:
    /**
     * @brief 构造函数
     */
    GPUMemoryPool();
    
    /**
     * @brief 析构函数
     */
    ~GPUMemoryPool();
    
    /**
     * @brief 初始化内存池
     */
    bool initialize(size_t initial_size = MemoryPoolConfig::DEFAULT_POOL_SIZE);
    
    /**
     * @brief 分配内存
     */
    void* allocate(size_t size, size_t alignment = MemoryPoolConfig::ALIGNMENT);
    
    /**
     * @brief 释放内存
     */
    bool deallocate(void *ptr);
    
    /**
     * @brief 分配512-bit袋鼠数据
     */
    void* allocate_kangaroo_512(int num_kangaroos);
    
    /**
     * @brief 分配512-bit跳跃表数据
     */
    void* allocate_jump_tables_512(int jump_count);
    
    /**
     * @brief 分配DP哈希表内存
     */
    void* allocate_dp_hashtable(size_t entry_count);
    
    /**
     * @brief 获取内存使用统计
     */
    void get_statistics(size_t &total_alloc, size_t &peak_usage, 
                       size_t &current_usage, double &fragmentation) const;
    
    /**
     * @brief 内存碎片整理
     */
    bool defragment();
    
    /**
     * @brief 重置内存池
     */
    void reset();
    
    /**
     * @brief 扩展内存池
     */
    bool expand_pool(size_t additional_size);
    
    /**
     * @brief 检查内存池健康状态
     */
    bool health_check() const;
    
private:
    /**
     * @brief 对齐大小
     */
    size_t align_size(size_t size, size_t alignment) const;
    
    /**
     * @brief 查找合适的内存块
     */
    MemoryBlock* find_suitable_block(size_t size, size_t alignment);
    
    /**
     * @brief 分割内存块
     */
    bool split_block(MemoryBlock *block, size_t size);
    
    /**
     * @brief 合并相邻空闲块
     */
    void coalesce_free_blocks();
    
    /**
     * @brief 创建新的内存池
     */
    bool create_new_pool(size_t size);
};

/**
 * @brief 全局GPU内存池实例
 */
extern GPUMemoryPool g_gpu_memory_pool;

/**
 * @brief 便捷分配函数
 */
inline void* gpu_malloc_512(size_t size) {
    return g_gpu_memory_pool.allocate(size);
}

inline bool gpu_free_512(void *ptr) {
    return g_gpu_memory_pool.deallocate(ptr);
}

/**
 * @brief 512-bit数据专用分配器
 */
template<typename T>
class GPU512Allocator {
public:
    static T* allocate(size_t count) {
        size_t size = count * sizeof(T);
        return static_cast<T*>(g_gpu_memory_pool.allocate(size));
    }
    
    static bool deallocate(T* ptr) {
        return g_gpu_memory_pool.deallocate(ptr);
    }
};

/**
 * @brief RAII GPU内存管理器
 */
template<typename T>
class GPUMemoryGuard {
private:
    T* ptr;
    size_t count;
    
public:
    GPUMemoryGuard(size_t n) : count(n) {
        ptr = GPU512Allocator<T>::allocate(count);
    }
    
    ~GPUMemoryGuard() {
        if (ptr) {
            GPU512Allocator<T>::deallocate(ptr);
        }
    }
    
    T* get() const { return ptr; }
    T* operator->() const { return ptr; }
    T& operator*() const { return *ptr; }
    
    // 禁止拷贝
    GPUMemoryGuard(const GPUMemoryGuard&) = delete;
    GPUMemoryGuard& operator=(const GPUMemoryGuard&) = delete;
    
    // 支持移动
    GPUMemoryGuard(GPUMemoryGuard&& other) noexcept 
        : ptr(other.ptr), count(other.count) {
        other.ptr = nullptr;
        other.count = 0;
    }
    
    GPUMemoryGuard& operator=(GPUMemoryGuard&& other) noexcept {
        if (this != &other) {
            if (ptr) {
                GPU512Allocator<T>::deallocate(ptr);
            }
            ptr = other.ptr;
            count = other.count;
            other.ptr = nullptr;
            other.count = 0;
        }
        return *this;
    }
};

/**
 * @brief 内存池性能监控
 */
class MemoryPoolMonitor {
private:
    static MemoryPoolMonitor* instance;
    std::vector<double> allocation_times;
    std::vector<size_t> allocation_sizes;
    std::mutex monitor_mutex;
    
public:
    static MemoryPoolMonitor& getInstance();
    
    void record_allocation(size_t size, double time_ms);
    void record_deallocation(double time_ms);
    void get_performance_stats(double &avg_alloc_time, double &avg_dealloc_time,
                              size_t &total_allocations) const;
    void reset_stats();
};

#endif // GPU_MEMORY_POOL_CUH
