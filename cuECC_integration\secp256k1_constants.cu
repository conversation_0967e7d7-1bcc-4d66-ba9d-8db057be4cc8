/*
 * secp256k1_constants.cu
 * 
 * secp256k1椭圆曲线常量定义
 * 避免多重定义问题
 */

#include "curve/point.cuh"

// secp256k1椭圆曲线参数定义
__constant__ u64 A[4] = {0, 0, 0, 0};

__constant__ u64 B[4] = {7, 0, 0, 0};

__constant__ u64 P[4] = {0xfffffffefffffc2f, 0xffffffffffffffff,
                         0xffffffffffffffff, 0xffffffffffffffff};

__constant__ cuECC_Point_Internal G = {{0x59f2815b16f81798, 0x029bfcdb2dce28d9,
                                        0x55a06295ce870b07, 0x79be667ef9dcbbac},
                                       {0x9c47d08ffb10d4b8, 0xfd17b448a6855419,
                                        0x5da4fbfc0e1108a8, 0x483ada7726a3c465}};
