#ifndef SHARDED_HASHTABLE_H
#define SHARDED_HASHTABLE_H

#include <unordered_map>
#include <vector>
#include <memory>
#include <mutex>
#include <atomic>
#include "HashTable.h"
#include "SECPK1/Int.h"

/**
 * @file sharded_hashtable.h
 * @brief 分片哈希表系统 - 突破单一哈希表内存限制
 * 
 * 支持TB级别的哈希表，通过分片技术实现大范围搜索
 * 兼容现有HashTable接口，无缝集成
 */

// 128-bit键类型 (用于分片索引)
struct uint128_t {
    uint64_t low;
    uint64_t high;
    
    uint128_t() : low(0), high(0) {}
    uint128_t(uint64_t l, uint64_t h) : low(l), high(h) {}
    
    bool operator==(const uint128_t& other) const {
        return low == other.low && high == other.high;
    }
    
    bool operator<(const uint128_t& other) const {
        if (high != other.high) return high < other.high;
        return low < other.low;
    }
};

// 128-bit哈希函数
struct uint128_hash {
    size_t operator()(const uint128_t& key) const {
        return std::hash<uint64_t>{}(key.low) ^ 
               (std::hash<uint64_t>{}(key.high) << 1);
    }
};

// 分片配置
struct ShardConfig {
    static const size_t SHARD_SIZE = 4ULL * 1024 * 1024 * 1024;  // 4GB per shard
    static const uint32_t MAX_SHARDS = 256;  // 最大256个分片 (1TB)
    static const uint32_t DEFAULT_SHARDS = 16;  // 默认16个分片 (64GB)
};

// 分片统计信息
struct ShardStatistics {
    uint64_t total_items;           // 总项目数
    uint64_t total_collisions;      // 总碰撞数
    uint64_t memory_used;           // 使用的内存
    double load_factor;             // 负载因子
    double collision_rate;          // 碰撞率
    uint64_t operations_count;      // 操作计数
    
    ShardStatistics() : total_items(0), total_collisions(0), memory_used(0),
                       load_factor(0.0), collision_rate(0.0), operations_count(0) {}
};

/**
 * @brief 分片哈希表类
 */
class ShardedHashTable {
private:
    // 分片存储 - 简化为uint64_t数据
    std::vector<std::unordered_map<uint128_t, uint64_t, uint128_hash>> shards;
    std::vector<std::mutex> shard_mutexes;
    uint32_t num_shards;
    
    // 统计信息
    std::vector<std::atomic<uint64_t>> shard_sizes;
    std::vector<std::atomic<uint64_t>> shard_collisions;
    std::atomic<uint64_t> total_items;
    std::atomic<uint64_t> total_memory_used;
    
    // 配置参数
    size_t target_memory_gb;
    double max_load_factor;
    bool auto_resize_enabled;
    
    // 性能监控
    std::atomic<uint64_t> insert_operations;
    std::atomic<uint64_t> lookup_operations;
    std::atomic<uint64_t> collision_found_count;
    
public:
    /**
     * @brief 构造函数
     * @param target_memory_gb 目标内存大小(GB)
     * @param num_shards_hint 分片数量提示
     */
    ShardedHashTable(size_t target_memory_gb = 64, uint32_t num_shards_hint = 0);
    
    /**
     * @brief 析构函数
     */
    ~ShardedHashTable();
    
    /**
     * @brief 插入项目
     * @param key 128位键
     * @param data 项目数据
     * @return 是否发现碰撞
     */
    bool insert(const uint128_t& key, uint64_t data);
    
    /**
     * @brief 查找项目
     * @param key 128位键
     * @param data 输出项目数据
     * @return 是否找到
     */
    bool find(const uint128_t& key, uint64_t& data);
    
    /**
     * @brief 简化接口：从64位值创建键
     * @param value 64位值
     * @return 128位键
     */
    uint128_t makeKey(uint64_t value) const;

    /**
     * @brief 简化接口：插入数据
     * @param data 数据值
     * @return 是否发现碰撞
     */
    bool insertData(uint64_t data);
    
    /**
     * @brief 获取分片索引
     * @param key 128位键
     * @return 分片索引
     */
    uint32_t getShardIndex(const uint128_t& key) const;
    
    /**
     * @brief 清空所有分片
     */
    void clear();
    
    /**
     * @brief 重新平衡分片
     */
    void rebalanceShards();
    
    /**
     * @brief 自动调整分片数量
     * @param new_shard_count 新的分片数量
     */
    void resizeShards(uint32_t new_shard_count);
    
    /**
     * @brief 获取统计信息
     * @return 统计信息结构
     */
    ShardStatistics getStatistics() const;
    
    /**
     * @brief 打印统计信息
     */
    void printStatistics() const;
    
    /**
     * @brief 打印详细的分片信息
     */
    void printDetailedShardInfo() const;
    
    /**
     * @brief 获取总项目数
     */
    size_t getTotalItems() const { return total_items.load(); }
    
    /**
     * @brief 获取内存使用量
     */
    size_t getMemoryUsage() const { return total_memory_used.load(); }
    
    /**
     * @brief 获取分片数量
     */
    uint32_t getShardCount() const { return num_shards; }
    
    /**
     * @brief 检查碰撞 - 简化版本
     * @param key1 第一个键
     * @param key2 第二个键
     * @return 是否为有效碰撞
     */
    bool checkCollision(const uint128_t& key1, const uint128_t& key2);
    
    /**
     * @brief 设置最大负载因子
     * @param factor 负载因子 (0.1-0.9)
     */
    void setMaxLoadFactor(double factor);
    
    /**
     * @brief 启用/禁用自动调整大小
     * @param enabled 是否启用
     */
    void setAutoResize(bool enabled) { auto_resize_enabled = enabled; }
    
    /**
     * @brief 获取性能指标
     */
    struct PerformanceMetrics {
        double insert_rate;         // 插入速率 (ops/sec)
        double lookup_rate;         // 查找速率 (ops/sec)
        double collision_rate;      // 碰撞率
        double memory_efficiency;   // 内存效率
        double load_balance;        // 负载均衡度
    };
    
    PerformanceMetrics getPerformanceMetrics() const;
    
    /**
     * @brief 导出分片数据 (用于备份)
     * @param filename 文件名
     * @return 是否成功
     */
    bool exportToFile(const std::string& filename) const;
    
    /**
     * @brief 从文件导入分片数据
     * @param filename 文件名
     * @return 是否成功
     */
    bool importFromFile(const std::string& filename);
    
private:
    /**
     * @brief 初始化分片
     */
    void initializeShards();
    
    /**
     * @brief 更新统计信息
     */
    void updateStatistics();
    
    /**
     * @brief 检查是否需要调整大小
     */
    bool needsResize() const;
    
    /**
     * @brief 计算最优分片数量
     * @param total_items 总项目数
     * @param target_memory 目标内存
     * @return 最优分片数量
     */
    uint32_t calculateOptimalShardCount(uint64_t total_items, size_t target_memory) const;
    
    /**
     * @brief 高质量哈希函数
     * @param key 128位键
     * @return 哈希值
     */
    uint64_t highQualityHash(const uint128_t& key) const;
    
    /**
     * @brief 计算负载均衡度
     * @return 负载均衡度 (0-1)
     */
    double calculateLoadBalance() const;
};

/**
 * @brief 分片哈希表工厂类
 */
class ShardedHashTableFactory {
public:
    /**
     * @brief 创建适合当前系统的分片哈希表
     * @param range_bits 搜索范围位数
     * @param kangaroo_count 袋鼠数量
     * @return 分片哈希表实例
     */
    static std::unique_ptr<ShardedHashTable> createOptimal(uint32_t range_bits, 
                                                           uint64_t kangaroo_count);
    
    /**
     * @brief 创建内存限制的分片哈希表
     * @param max_memory_gb 最大内存限制(GB)
     * @return 分片哈希表实例
     */
    static std::unique_ptr<ShardedHashTable> createMemoryLimited(size_t max_memory_gb);
    
    /**
     * @brief 创建高性能分片哈希表
     * @param target_ops_per_sec 目标操作速率
     * @return 分片哈希表实例
     */
    static std::unique_ptr<ShardedHashTable> createHighPerformance(uint64_t target_ops_per_sec);
};

#endif // SHARDED_HASHTABLE_H
