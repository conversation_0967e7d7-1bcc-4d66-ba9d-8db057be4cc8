# 📋 Phase 1: 现代化基础设施详细计划

## 🎯 目标概述

将Kangaroo项目现代化，支持NVIDIA全系列GPU (SM 5.2-9.0)，确保Windows和Linux平台完全兼容。

## 🔧 技术实现

### 1.1 编译系统升级

#### CMakeLists.txt 全面重构
```cmake
cmake_minimum_required(VERSION 3.18)
project(Kangaroo LANGUAGES CXX CUDA)

# 设置标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CUDA_STANDARD 17)
set(CMAKE_CUDA_STANDARD_REQUIRED ON)

# 全GPU架构支持 (<PERSON>到Hopper)
set(CMAKE_CUDA_ARCHITECTURES "52;60;61;70;75;80;86;89;90")

# 平台检测和配置
if(WIN32)
    add_definitions(-DWIN64 -D_CRT_SECURE_NO_WARNINGS -DWITHGPU)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /bigobj")
    set(PLATFORM_LIBS ws2_32)
elseif(UNIX AND NOT APPLE)
    add_definitions(-DLINUX -DWITHGPU)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -pthread")
    set(PLATFORM_LIBS pthread)
endif()

# GPU能力检测
add_definitions(-DUSE_MODERN_CUDA -DAUTO_GPU_DETECT)

# 编译优化
if(CMAKE_BUILD_TYPE STREQUAL "Release")
    if(MSVC)
        target_compile_options(kangaroo PRIVATE 
            $<$<COMPILE_LANGUAGE:CXX>:/O2 /Ob2>
            $<$<COMPILE_LANGUAGE:CUDA>:-O3 --use_fast_math>
        )
    else()
        target_compile_options(kangaroo PRIVATE 
            $<$<COMPILE_LANGUAGE:CXX>:-O3 -march=native>
            $<$<COMPILE_LANGUAGE:CUDA>:-O3 --use_fast_math>
        )
    endif()
endif()

# CUDA特性启用
target_compile_options(kangaroo PRIVATE 
    $<$<COMPILE_LANGUAGE:CUDA>:--expt-relaxed-constexpr>
    $<$<COMPILE_LANGUAGE:CUDA>:--expt-extended-lambda>
    $<$<COMPILE_LANGUAGE:CUDA>:-lineinfo>
)
```

### 1.2 GPU自动检测系统

#### gpu_detector.h
```cpp
#ifndef GPU_DETECTOR_H
#define GPU_DETECTOR_H

#include <cuda_runtime.h>
#include <vector>
#include <string>

struct GPUInfo {
    int device_id;
    std::string name;
    int major;
    int minor;
    size_t total_memory;
    int multiprocessor_count;
    int max_threads_per_block;
    int max_threads_per_multiprocessor;
    bool supports_cooperative_groups;
    bool supports_unified_memory;
};

class GPUDetector {
public:
    static std::vector<GPUInfo> detectAllGPUs();
    static GPUInfo getBestGPU();
    static bool isArchitectureSupported(int major, int minor);
    static std::string getArchitectureName(int major, int minor);
    static void printGPUInfo(const GPUInfo& info);
    static void printAllGPUs();
};

#endif // GPU_DETECTOR_H
```

#### gpu_detector.cu
```cpp
#include "gpu_detector.h"
#include <iostream>
#include <algorithm>

std::vector<GPUInfo> GPUDetector::detectAllGPUs() {
    std::vector<GPUInfo> gpus;
    int device_count;
    
    cudaError_t error = cudaGetDeviceCount(&device_count);
    if (error != cudaSuccess) {
        std::cerr << "CUDA error: " << cudaGetErrorString(error) << std::endl;
        return gpus;
    }
    
    for (int i = 0; i < device_count; i++) {
        cudaDeviceProp prop;
        cudaGetDeviceProperties(&prop, i);
        
        GPUInfo info;
        info.device_id = i;
        info.name = prop.name;
        info.major = prop.major;
        info.minor = prop.minor;
        info.total_memory = prop.totalGlobalMem;
        info.multiprocessor_count = prop.multiProcessorCount;
        info.max_threads_per_block = prop.maxThreadsPerBlock;
        info.max_threads_per_multiprocessor = prop.maxThreadsPerMultiProcessor;
        
        // 检查高级特性支持
        info.supports_cooperative_groups = (prop.major >= 6);
        info.supports_unified_memory = (prop.major >= 6);
        
        gpus.push_back(info);
    }
    
    return gpus;
}

GPUInfo GPUDetector::getBestGPU() {
    auto gpus = detectAllGPUs();
    if (gpus.empty()) {
        throw std::runtime_error("No CUDA-capable GPU found");
    }
    
    // 选择最强的GPU (基于计算能力和内存)
    auto best = std::max_element(gpus.begin(), gpus.end(), 
        [](const GPUInfo& a, const GPUInfo& b) {
            // 优先级: 计算能力 > 内存大小 > SM数量
            if (a.major != b.major) return a.major < b.major;
            if (a.minor != b.minor) return a.minor < b.minor;
            if (a.total_memory != b.total_memory) return a.total_memory < b.total_memory;
            return a.multiprocessor_count < b.multiprocessor_count;
        });
    
    return *best;
}

bool GPUDetector::isArchitectureSupported(int major, int minor) {
    // 支持Maxwell及以上架构
    return (major >= 5) && (major < 10);
}

std::string GPUDetector::getArchitectureName(int major, int minor) {
    switch (major) {
        case 5: return (minor == 2) ? "Maxwell" : "Maxwell 2.0";
        case 6: return (minor == 0) ? "Pascal" : "Pascal";
        case 7: return (minor == 0) ? "Volta" : "Turing";
        case 8: return (minor < 7) ? "Ampere" : "Ada Lovelace";
        case 9: return "Hopper";
        default: return "Unknown";
    }
}

void GPUDetector::printGPUInfo(const GPUInfo& info) {
    std::cout << "GPU #" << info.device_id << ": " << info.name << std::endl;
    std::cout << "  Compute Capability: " << info.major << "." << info.minor 
              << " (" << getArchitectureName(info.major, info.minor) << ")" << std::endl;
    std::cout << "  Memory: " << (info.total_memory / (1024*1024)) << " MB" << std::endl;
    std::cout << "  Multiprocessors: " << info.multiprocessor_count << std::endl;
    std::cout << "  Max Threads/Block: " << info.max_threads_per_block << std::endl;
    std::cout << "  Supported: " << (isArchitectureSupported(info.major, info.minor) ? "Yes" : "No") << std::endl;
}

void GPUDetector::printAllGPUs() {
    auto gpus = detectAllGPUs();
    std::cout << "=== GPU Detection Results ===" << std::endl;
    std::cout << "Found " << gpus.size() << " CUDA-capable GPU(s)" << std::endl;
    std::cout << std::endl;
    
    for (const auto& gpu : gpus) {
        printGPUInfo(gpu);
        std::cout << std::endl;
    }
}
```

### 1.3 跨平台兼容性层

#### platform_utils.h
```cpp
#ifndef PLATFORM_UTILS_H
#define PLATFORM_UTILS_H

#ifdef _WIN32
    #include <windows.h>
    #include <winsock2.h>
    #define PLATFORM_WINDOWS
#else
    #include <unistd.h>
    #include <sys/time.h>
    #include <pthread.h>
    #define PLATFORM_LINUX
#endif

class PlatformUtils {
public:
    static void initializePlatform();
    static void cleanupPlatform();
    static uint64_t getCurrentTimeMs();
    static void sleepMs(uint32_t milliseconds);
    static int getCPUCoreCount();
    static size_t getSystemMemory();
    static std::string getPlatformName();
};

#endif // PLATFORM_UTILS_H
```

### 1.4 现代CUDA特性启用

#### modern_cuda.h
```cpp
#ifndef MODERN_CUDA_H
#define MODERN_CUDA_H

#include <cuda_runtime.h>
#include <cooperative_groups.h>

// CUDA错误检查宏
#define CUDA_CHECK(call) do { \
    cudaError_t error = call; \
    if (error != cudaSuccess) { \
        fprintf(stderr, "CUDA error at %s:%d - %s\n", __FILE__, __LINE__, \
                cudaGetErrorString(error)); \
        exit(1); \
    } \
} while(0)

// 现代CUDA特性检测
class ModernCUDA {
public:
    static bool supportsCooperativeGroups(int device_id);
    static bool supportsUnifiedMemory(int device_id);
    static bool supportsCudaGraphs(int device_id);
    static void enableOptimalSettings(int device_id);
};

#endif // MODERN_CUDA_H
```

## 📊 验证标准

### 编译验证
- [ ] Windows MSVC 2019+ 编译通过
- [ ] Linux GCC 9+ 编译通过
- [ ] 所有GPU架构 (SM 5.2-9.0) 支持
- [ ] 无编译警告

### 功能验证
- [ ] GPU自动检测正常
- [ ] 已知私钥测试通过
- [ ] 跨平台兼容性测试
- [ ] 性能基准测试

### 性能目标
- [ ] 编译时间减少50%
- [ ] GPU检测时间 < 1秒
- [ ] 基础性能无回退

## 🚀 实施步骤

1. **Day 1**: CMakeLists.txt重构 + GPU检测系统
2. **Day 2**: 跨平台兼容性 + 现代CUDA特性
3. **验证**: 全平台编译测试 + 功能验证

## 📁 文件清单

```
optimizations/phase1/
├── gpu_detector.h
├── gpu_detector.cu
├── platform_utils.h
├── platform_utils.cpp
├── modern_cuda.h
├── modern_cuda.cu
└── CMakeLists_phase1.txt
```
